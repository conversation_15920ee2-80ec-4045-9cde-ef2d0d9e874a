import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON>, Button, Alert, Spinner } from 'react-bootstrap';
import { createMasterOption } from '../../Redux/masterOptionSlice';

const MasterOptionForm = () => {
  const dispatch = useDispatch();
  const { isLoading, error, option } = useSelector((state) => state.masterOption);

  const [formData, setFormData] = useState({
    title: '',
    option_content: '',
    conditions: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    dispatch(createMasterOption(formData));
  };

  return (
    <div className="container mt-5">
      <h2>Create Master Option</h2>
      <Form onSubmit={handleSubmit}>
        <Form.Group className="mb-3">
          <Form.Label>Title</Form.Label>
          <Form.Control
            type="text"
            placeholder="Enter option title"
            name="title"
            value={formData.title}
            onChange={handleChange}
          />
        </Form.Group>
        <Form.Group className="mb-3">
          <Form.Label>Option Content</Form.Label>
          <Form.Control
            as="textarea"
            rows={4}
            placeholder="Enter option content"
            name="option_content"
            value={formData.option_content}
            onChange={handleChange}
          />
        </Form.Group>
        <Form.Group className="mb-3">
          <Form.Label>Conditions</Form.Label>
          <Form.Control
            type="text"
            placeholder="Enter conditions"
            name="conditions"
            value={formData.conditions}
            onChange={handleChange}
          />
        </Form.Group>
        <Button variant="primary" type="submit" disabled={isLoading}>
          {isLoading ? <Spinner animation="border" size="sm" /> : 'Submit'}
        </Button>
      </Form>
      {error && <Alert variant="danger" className="mt-3">{error}</Alert>}
      {option && <Alert variant="success" className="mt-3">Option created successfully!</Alert>}
    </div>
  );
};

export default MasterOptionForm;
