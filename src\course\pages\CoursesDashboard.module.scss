.cardContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
}

.courseCard {
    width: 100%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    transition: transform 0.3s ease;

    &:hover {
        transform: translateY(-5px);
    }

    .card-title {
        font-size: 1.2rem;
        font-weight: bold;
    }

    .card-text {
        font-size: 0.9rem;
    }

    .btn {
        margin-right: 5px;
    }
}

.pagination {
    display: inline-flex;
    list-style: none;
    padding: 0;
    margin: 0;
}

.page-item {
    margin: 0 5px;
}

.page-link {
    padding: 10px 15px;
    border: 1px solid #ccc;
    cursor: pointer;
}

.page-link:hover {
    background-color: #f0f0f0;
}

.active .page-link {
    background-color: #007bff;
    color: white;
}

.disabled .page-link {
    cursor: not-allowed;
    opacity: 0.5;
}
