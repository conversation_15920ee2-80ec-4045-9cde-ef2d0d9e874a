import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Base URL from environment variables
const BASE_URL = import.meta.env.VITE_BASE_URL; // Use the base URL from environment variable

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor; // Adjust this path to match your state structure
  return accessToken;
};

// Create Test Paper Thunk
export const createTestPaper = createAsyncThunk(
  'paperEngine/createTestPaper',
  async (testPaperData, { rejectWithValue, getState }) => {
    try {
      const token = getAuthToken(getState);
      if (!token) {
        throw new Error('No access token found');
      }
      const response = await axios.post(
        `${BASE_URL}api/test-papers/`,
        testPaperData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Create Test Paper Error:', error);
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Create Test Pattern Thunk
export const createTestPattern = createAsyncThunk(
  'paperEngine/createTestPattern',
  async (testPatternData, { rejectWithValue, getState }) => {
    try {
      const token = getAuthToken(getState);
      if (!token) {
        throw new Error('No access token found');
      }
      const response = await axios.post(
        `${BASE_URL}${import.meta.env.VITE_CREATE_TEST_PATTERN}`,
        testPatternData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Create Test Pattern Error:', error);
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Get Test Patterns Thunk
export const getTestPatterns = createAsyncThunk(
  'paperEngine/getTestPatterns',
  async (_, { rejectWithValue, getState }) => {
    try {
      const token = getAuthToken(getState);
      if (!token) {
        throw new Error('No access token found');
      }
      const response = await axios.get(`${BASE_URL}${import.meta.env.VITE_GET_TEST_PATTERNS}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Get Test Patterns Error:', error);
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Get Single Test Pattern Thunk
export const getSingleTestPattern = createAsyncThunk(
  'paperEngine/getSingleTestPattern',
  async (id, { rejectWithValue, getState }) => {
    try {
      const token = getAuthToken(getState); // Get access token here
      if (!token) {
        throw new Error('No access token found');
      }
      const response = await axios.get(`${BASE_URL}${import.meta.env.VITE_GET_TEST_PATTERN}${id}/`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Get Single Test Pattern Error:', error);
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Edit (PUT) Test Pattern Thunk
export const editTestPattern = createAsyncThunk(
  'paperEngine/editTestPattern',
  async ({ id, updatedData }, { rejectWithValue, getState }) => {
    try {
      const token = getAuthToken(getState); // Get access token here
      if (!token) {
        throw new Error('No access token found');
      }
      const response = await axios.put(
        `${BASE_URL}${import.meta.env.VITE_EDIT_TEST_PATTERN}${id}/`,
        updatedData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Edit Test Pattern Error:', error);
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Delete Test Pattern Thunk
export const deleteTestPattern = createAsyncThunk(
  'paperEngine/deleteTestPattern',
  async (id, { rejectWithValue, getState }) => {
    try {
      const token = getAuthToken(getState); // Get access token here
      if (!token) {
        throw new Error('No access token found');
      }
      await axios.delete(`${BASE_URL}${import.meta.env.VITE_DELETE_TEST_PATTERN}${id}/`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return id; // Return the ID to update the state
    } catch (error) {
      console.error('Delete Test Pattern Error:', error);
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Slice
const paperEngineSlice = createSlice({
  name: 'paperEngine',
  initialState: {
    testPatterns: [], // List of test patterns
    singleTestPattern: null, // The selected single test pattern
    status: 'idle', // idle, loading, succeeded, failed
    error: null, // Stores error messages
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Handle Get Test Patterns
      .addCase(getTestPatterns.pending, (state) => {
        state.status = 'loading';
        state.error = null; // Clear previous errors
      })
      .addCase(getTestPatterns.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.testPatterns = action.payload;
      })
      .addCase(getTestPatterns.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload || 'Failed to fetch test patterns';
        console.error('Get Test Patterns Error:', action.payload);
      })

      // Handle Get Single Test Pattern
      .addCase(getSingleTestPattern.pending, (state) => {
        state.status = 'loading';
        state.error = null; // Clear previous errors
      })
      .addCase(getSingleTestPattern.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.singleTestPattern = action.payload; // Store the single test pattern data
      })
      .addCase(getSingleTestPattern.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload || 'Failed to fetch test pattern';
        console.error('Get Single Test Pattern Error:', action.payload);
      })

      // Handle Create Test Pattern
      .addCase(createTestPattern.pending, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(createTestPattern.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.testPatterns.push(action.payload);
      })
      .addCase(createTestPattern.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload || 'Failed to create test pattern';
        console.error('Create Test Pattern Error:', action.payload);
      })

      // Handle Edit Test Pattern
      .addCase(editTestPattern.pending, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(editTestPattern.fulfilled, (state, action) => {
        state.status = 'succeeded';
        const index = state.testPatterns.findIndex(
          (pattern) => pattern.id === action.payload.id
        );
        if (index !== -1) {
          state.testPatterns[index] = action.payload;
        }
      })
      .addCase(editTestPattern.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload || 'Failed to edit test pattern';
        console.error('Edit Test Pattern Error:', action.payload);
      })

      // Handle Delete Test Pattern
      .addCase(deleteTestPattern.pending, (state) => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(deleteTestPattern.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.testPatterns = state.testPatterns.filter(
          (pattern) => pattern.id !== action.payload
        );
      })
      .addCase(deleteTestPattern.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload || 'Failed to delete test pattern';
        console.error('Delete Test Pattern Error:', action.payload);
      });
  },
});

export default paperEngineSlice.reducer;
