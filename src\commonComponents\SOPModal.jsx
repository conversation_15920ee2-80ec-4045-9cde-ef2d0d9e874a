import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Dropdown } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getSOP } from "../Redux/slice/sopSlice";

const SOPModal = ({ isOpen, onRequestClose }) => {
  const dispatch = useDispatch();
  const { sops, isLoading, error } = useSelector((state) => state.sop);
  const [selectedSop, setSelectedSop] = useState(null);

  // Fetch SOPs when modal opens
  useEffect(() => {
    if (isOpen) {
      dispatch(getSOP());
    }
  }, [isOpen, dispatch]);

  // Set the first SOP as selected when SOPs are loaded
  useEffect(() => {
    if (sops && sops.length > 0 && !selectedSop) {
      setSelectedSop(sops[0]);
    }
  }, [sops, selectedSop]);

  // Reset selected SOP when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedSop(null);
    }
  }, [isOpen]);

  const handleSopSelect = (sop) => {
    setSelectedSop(sop);
  };

  return (
    <Modal show={isOpen} onHide={onRequestClose} size="xl" centered>
      <Modal.Header closeButton>
        <Modal.Title className="d-flex align-items-center justify-content-between w-100">
          <span>Statement of Purpose (SOP)</span>
          {sops && sops.length > 1 && (
            <Dropdown>
              <Dropdown.Toggle variant="outline-primary" size="sm">
                {selectedSop ? selectedSop.name : 'Select SOP'}
              </Dropdown.Toggle>
              <Dropdown.Menu>
                {sops.map((sop) => (
                  <Dropdown.Item
                    key={sop.id}
                    onClick={() => handleSopSelect(sop)}
                    active={selectedSop?.id === sop.id}
                  >
                    {sop.name}
                  </Dropdown.Item>
                ))}
              </Dropdown.Menu>
            </Dropdown>
          )}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ height: "80vh", padding: 0 }}>
        {isLoading ? (
          <div className="d-flex justify-content-center align-items-center h-100">
            <div className="text-center">
              <Spinner animation="border" role="status" />
              <div className="mt-2">Loading SOP...</div>
            </div>
          </div>
        ) : error ? (
          <div className="d-flex justify-content-center align-items-center h-100">
            <Alert variant="danger" className="text-center">
              <Alert.Heading>Error Loading SOP</Alert.Heading>
              <p>{typeof error === 'string' ? error : 'Failed to load SOP. Please try again.'}</p>
            </Alert>
          </div>
        ) : selectedSop && selectedSop.pdf ? (
          <iframe
            src={selectedSop.pdf}
            title={`SOP PDF - ${selectedSop.name}`}
            style={{ width: "100%", height: "100%", border: "none", borderRadius: "8px" }}
            onError={() => {
              console.error('Failed to load PDF:', selectedSop.pdf);
            }}
          />
        ) : sops && sops.length === 0 ? (
          <div className="d-flex justify-content-center align-items-center h-100">
            <Alert variant="info" className="text-center">
              <Alert.Heading>No SOP Available</Alert.Heading>
              <p>No Statement of Purpose document is currently available.</p>
            </Alert>
          </div>
        ) : (
          <div className="d-flex justify-content-center align-items-center h-100">
            <Alert variant="warning" className="text-center">
              <Alert.Heading>PDF Not Available</Alert.Heading>
              <p>The selected SOP document could not be loaded.</p>
            </Alert>
          </div>
        )}
      </Modal.Body>
    </Modal>
  );
};

export default SOPModal;
