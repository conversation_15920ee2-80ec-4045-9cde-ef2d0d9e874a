import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Dropdown } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getSOP } from "../Redux/slice/sopSlice";

const SOPModal = ({ isOpen, onRequestClose }) => {
  const dispatch = useDispatch();
  const { sops, isLoading, error } = useSelector((state) => state.sop);
  const [selectedSop, setSelectedSop] = useState(null);
  const [iframeError, setIframeError] = useState(false);

  // Fetch SOPs when modal opens
  useEffect(() => {
    if (isOpen) {
      console.log('SOPModal: Fetching SOPs...');
      dispatch(getSOP());
      setIframeError(false); // Reset iframe error when modal opens
    }
  }, [isOpen, dispatch]);

  // Set the first SOP as selected when SOPs are loaded
  useEffect(() => {
    if (sops && sops.length > 0 && !selectedSop) {
      console.log('SOPModal: Setting first SOP as selected:', sops[0]);
      setSelectedSop(sops[0]);
    }
  }, [sops, selectedSop]);

  // Reset selected SOP when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedSop(null);
      setIframeError(false);
    }
  }, [isOpen]);

  // Debug: Log current state
  useEffect(() => {
    console.log('SOPModal State:', {
      isOpen,
      isLoading,
      error,
      sopsCount: sops?.length || 0,
      selectedSop: selectedSop?.name || 'None',
      selectedSopPdf: selectedSop?.pdf || 'None'
    });
  }, [isOpen, isLoading, error, sops, selectedSop]);

  const handleSopSelect = (sop) => {
    console.log('SOPModal: Selecting SOP:', sop);
    setSelectedSop(sop);
    setIframeError(false); // Reset iframe error when selecting new SOP
  };

  const handleIframeLoad = () => {
    console.log('SOPModal: PDF loaded successfully');
    setIframeError(false);
  };

  const handleIframeError = () => {
    console.error('SOPModal: Failed to load PDF:', selectedSop?.pdf);
    setIframeError(true);
  };

  return (
    <Modal show={isOpen} onHide={onRequestClose} size="xl" centered>
      <Modal.Header closeButton>
        <Modal.Title className="d-flex align-items-center justify-content-between w-100">
          <span>Statement of Purpose (SOP)</span>
          {sops && sops.length > 1 && (
            <Dropdown>
              <Dropdown.Toggle variant="outline-primary" size="sm">
                {selectedSop ? selectedSop.name : 'Select SOP'}
              </Dropdown.Toggle>
              <Dropdown.Menu>
                {sops.map((sop) => (
                  <Dropdown.Item
                    key={sop.id}
                    onClick={() => handleSopSelect(sop)}
                    active={selectedSop?.id === sop.id}
                  >
                    {sop.name}
                  </Dropdown.Item>
                ))}
              </Dropdown.Menu>
            </Dropdown>
          )}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ height: "80vh", padding: 0 }}>
        {isLoading ? (
          <div className="d-flex justify-content-center align-items-center h-100">
            <div className="text-center">
              <Spinner animation="border" role="status" />
              <div className="mt-2">Loading SOP...</div>
            </div>
          </div>
        ) : error ? (
          <div className="d-flex justify-content-center align-items-center h-100">
            <Alert variant="danger" className="text-center">
              <Alert.Heading>Error Loading SOP</Alert.Heading>
              <p>{typeof error === 'string' ? error : 'Failed to load SOP. Please try again.'}</p>
            </Alert>
          </div>
        ) : selectedSop && selectedSop.pdf && !iframeError ? (
          <div style={{ width: "100%", height: "100%", position: "relative" }}>
            <iframe
              src={selectedSop.pdf}
              title={`SOP PDF - ${selectedSop.name}`}
              style={{ width: "100%", height: "100%", border: "none", borderRadius: "8px" }}
              onLoad={handleIframeLoad}
              onError={handleIframeError}
            />
            {/* Debug info - remove in production */}
            <div style={{
              position: "absolute",
              top: "10px",
              right: "10px",
              background: "rgba(0,0,0,0.7)",
              color: "white",
              padding: "5px 10px",
              borderRadius: "4px",
              fontSize: "12px",
              zIndex: 1000
            }}>
              PDF: {selectedSop.name}
            </div>
          </div>
        ) : iframeError ? (
          <div className="d-flex justify-content-center align-items-center h-100">
            <Alert variant="danger" className="text-center">
              <Alert.Heading>PDF Loading Error</Alert.Heading>
              <p>Failed to load the PDF document.</p>
              <p><strong>URL:</strong> {selectedSop?.pdf}</p>
              <Button
                variant="outline-primary"
                onClick={() => window.open(selectedSop?.pdf, '_blank')}
              >
                Open PDF in New Tab
              </Button>
            </Alert>
          </div>
        ) : sops && sops.length === 0 ? (
          <div className="d-flex justify-content-center align-items-center h-100">
            <Alert variant="info" className="text-center">
              <Alert.Heading>No SOP Available</Alert.Heading>
              <p>No Statement of Purpose document is currently available.</p>
            </Alert>
          </div>
        ) : (
          <div className="d-flex justify-content-center align-items-center h-100">
            <Alert variant="warning" className="text-center">
              <Alert.Heading>PDF Not Available</Alert.Heading>
              <p>The selected SOP document could not be loaded.</p>
              {selectedSop && (
                <div className="mt-3">
                  <p><strong>SOP Name:</strong> {selectedSop.name}</p>
                  <p><strong>PDF URL:</strong> {selectedSop.pdf || 'Not provided'}</p>
                  {selectedSop.pdf && (
                    <Button
                      variant="outline-primary"
                      onClick={() => window.open(selectedSop.pdf, '_blank')}
                    >
                      Try Opening in New Tab
                    </Button>
                  )}
                </div>
              )}
            </Alert>
          </div>
        )}
      </Modal.Body>
    </Modal>
  );
};

export default SOPModal;
