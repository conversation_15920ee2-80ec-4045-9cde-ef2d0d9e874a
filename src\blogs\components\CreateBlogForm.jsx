import React, { useState } from "react";
import { useDispatch, useSelector  } from "react-redux";
import { toast } from "react-hot-toast";
import BlogForm from "../components/BlogForm";
import { createBlog } from "../../redux/slice/blogSlice";
// import parse from "html-react-parser"; // Safely parsing HTML


const CreateBlogForm = ({ fetchBlogs }) => { 
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const contributorProfileId = useSelector((state) => state.contributor.contributorProfileId || null);


  const initialValues = {
    title: "",
    author: contributorProfileId,
    introduction: "",
    content: "",
    short_content: "",
    internal_link: "",
    external_link: "",
    image: null,
    image_caption: "",
    meta_title: "",
    meta_description: "",
    meta_keywords: "",
    canonical_url: "",
  };

  // const initialValues = {
  //   title: "Understanding React Components: A Beginner's Guide",
  //   author: 1, // Assuming `contributorProfileId` is 1 (change as needed)
  //   introduction: "This blog post is a comprehensive guide to understanding React components for beginners. Whether you're just starting with React or looking to refresh your knowledge, this guide will help you grasp the fundamentals.",
  //   content: "<p>React is a popular JavaScript library used to build user interfaces. Components are the building blocks of a React application. They allow you to break down your UI into smaller, reusable pieces. In this article, we'll go over the different types of components in React, how to create them, and how to use them in your app.</p><h2>What is a React Component?</h2><p>A component is essentially a function or class that returns a part of the UI. Components can be stateful or stateless, and they can accept props, which are inputs passed down from a parent component.</p><h2>Types of React Components</h2><p>There are two main types of components in React: Functional components and Class components. Functional components are simpler and are generally recommended for most cases.</p>",
  //   short_content: "Learn about React components, their types, and how to use them to build dynamic user interfaces in this beginner's guide.",
  //   internal_link: "https://shashtrarth.com/blogs/internal-page", // Example of internal link
  //   external_link: "https://shashtrarth.com/resource", // Example of external link
  //   image: "", // Example image URL
  //   image_caption: "An example of React components", // Caption for the image
  //   meta_title: "Understanding React Components: A Beginner's Guide",
  //   meta_description: "Learn the basics of React components in this beginner's guide, including types, usage, and examples.",
  //   meta_keywords: "React, Components, Beginner's Guide, JavaScript, UI Development",
  //   canonical_url: "https://shashtrarth.com/react-components-guide",
  // };
  

  const handleCreate = async (data) => {
   
    const defaultImage = `${import.meta.env.VITE_WEBSITE_DOMAIN}/images/default.png`;
   
     // The original title
     const title = data.title; // The original title
     const slug = title
       .toLowerCase()                    // Convert to lowercase
       .trim()                           // Trim any leading or trailing spaces
       .replace(/\s+/g, '-')             // Replace spaces with dashes
       .replace(/[^a-z0-9\-]/g, '');     // Remove any characters that are not lowercase letters, numbers, or dashes
     
     // Concatenate the VITE_HOSTED_DOMAIN with the slug
     const canonicalUrl = `http://${import.meta.env.VITE_HOSTED_DOMAIN}/blog/${slug}`;
     
     console.log(canonicalUrl); // Output the full URL

     
    let uploadedImageUrl = defaultImage;

      if (data.image) {
        // Generate the dynamic URL based on file name and extension
        const fileName = data.image.name.replace(/\s+/g, "-").toLowerCase(); // Replace spaces with hyphens
        const fileExtension = fileName.split(".").pop(); // Get the file extension
        uploadedImageUrl = `${import.meta.env.VITE_WEBSITE_DOMAIN}/media/blog_images/${fileName}`;
      }
  
    const openGraph = {
      "og:title": data.meta_title || data.title,
      "og:description": data.meta_description || data.introduction,
      "og:image": uploadedImageUrl,
      // "og:image": data.image ? URL.createObjectURL(data.image) : defaultImage,
    };
  
    const twitterCards = {
      "twitter:title": data.meta_title || data.title,
      "twitter:description": data.meta_description || data.introduction,
      "twitter:image": uploadedImageUrl,
      // "twitter:image": data.image ? URL.createObjectURL(data.image) : defaultImage,
    };
  
    const breadcrumbSchema = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        { "@type": "ListItem", position: 1, name: "Home", item: `${import.meta.env.VITE_HOSTED_DOMAIN}/` },
        { "@type": "ListItem", position: 2, name: "Blog", item: `${import.meta.env.VITE_HOSTED_DOMAIN}/blogs` },
        { "@type": "ListItem", position: 3, name: data.title, item: canonicalUrl },
      ],
    };
  
    const articleSchema = {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": data.meta_title || data.title,
      "image": [uploadedImageUrl],
      // "image": [data.image ? URL.createObjectURL(data.image) : defaultImage],
      "author": data.author || "Anonymous",
      "publisher": {
        "@type": "Organization",
        "name": "Your Website",
        "logo": { "@type": "ImageObject", url: `https://${import.meta.env.VITE_WEBSITE_DOMAIN}/images/logo.png` },
      },
      "datePublished": new Date().toISOString(),
      "dateModified": new Date().toISOString(),
    };
  
    const blogData = {
      ...data,
      canonical_url: canonicalUrl,
      // Stringify the JSON objects before adding them to the formData
      open_graph: JSON.stringify(openGraph),
      twitter_cards: JSON.stringify(twitterCards),
      breadcrumb_schema: JSON.stringify(breadcrumbSchema),
      article_schema: JSON.stringify(articleSchema),
    };
  
    console.log("BLOG DATA", blogData);
  
    setLoading(true);
    try {
      await dispatch(createBlog(blogData)).unwrap();
      toast.success("Blog created successfully!");
      fetchBlogs();  // Call fetchBlogs to refresh the list
    } catch (error) {
      toast.error(error.message || "Error creating blog.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <h2 className="text-start text-success my-3 px-3" style={{ fontSize: "1.5rem" }}>
        Create Blogs
      </h2>
      <BlogForm
        initialValues={initialValues}
        onSubmit={handleCreate}
        loading={loading}
      />
    </>
  );
};

export default CreateBlogForm;
