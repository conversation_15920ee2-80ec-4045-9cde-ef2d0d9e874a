import React, { useState, useEffect } from "react"; 
import ReactPaginate from "react-paginate";
import { Card, Button, Row, Col, Form, Container, Modal } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getCourses, getCourse, updateCourse, deleteCourse } from "../../redux/slice/courseSlice"; // Import necessary actions
import { Link, useNavigate } from "react-router-dom"; // Import Link for navigation
import { FaPlusCircle } from "react-icons/fa"; // React icon for the button
import NavigationBar from "../../commonComponents/NavigationBar";

const CoursesDashboard = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedCourse, setSelectedCourse] = useState(null);  // For the course to edit
  const [showEditModal, setShowEditModal] = useState(false); // For showing the edit modal
  const [courseData, setCourseData] = useState({ name: "", description: "" }); // To store form data
  const [showViewModal, setShowViewModal] = useState(false); // For showing the view course modal
  const coursesPerPage = 8; // Number of courses per page

  // Accessing the courses state from Redux store
  const dispatch = useDispatch();
  const navigate = useNavigate(); // For navigation after deletion
  const { courses, isLoading, error } = useSelector((state) => state.course);

  // Fetch courses on component mount
  useEffect(() => {
    dispatch(getCourses());
  }, [dispatch]);

  // Fetch course details when "Edit" button is clicked
  const handleEditCourse = (course_id) => {
    dispatch(getCourse(course_id)).then((action) => {
      if (action.type === 'course/getCourse/fulfilled') {
        const course = action.payload;
        setCourseData({ name: course.name, description: course.description });
        setSelectedCourse(course);
        setShowEditModal(true);
      }
    });
  };

  // Handle search functionality
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(0); // Reset to first page after search
  };

  // Handle pagination
  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected);
  };

  // Get current courses to display on the current page
  const indexOfLastCourse = (currentPage + 1) * coursesPerPage;
  const indexOfFirstCourse = indexOfLastCourse - coursesPerPage;
  const currentCourses = courses.slice(indexOfFirstCourse, indexOfLastCourse);

  // Handle course deletion
  const handleDeleteCourse = (course_id) => {
    dispatch(deleteCourse(course_id)).then(() => {
      dispatch(getCourses()) // Navigate to courses dashboard after deletion
    });
  };

  // Handle form submission to update the course
  const handleUpdateCourse = () => {
    const updatedCourse = { name: courseData.name, description: courseData.description };
    dispatch(updateCourse({ course_id: selectedCourse.course_id, updatedData: updatedCourse })).then(() => {
      setShowEditModal(false); // Close the modal
      dispatch(getCourses()); // Re-fetch the courses
    });
  };

  // Filtered courses based on search query
  const filteredCourses = courses.filter((course) =>
    course.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle viewing course details
  const handleViewCourse = (course) => {
    setSelectedCourse(course);
    setShowViewModal(true); // Open the view modal
  };

  return (
    <>
      <NavigationBar />
      <Container style={{ height: "90vh", paddingTop: "20px" }}>
        <h2 className="text-center text-success" style={{ fontSize: "1.9rem", margin: "1rem" }}>
          Courses Dashboard
        </h2>

        {/* Search Bar */}
        <Row className="mb-4 justify-content-center">
          <Col xs={12} sm={8} md={6} lg={5} className="d-flex justify-content-between">
            <Form.Control
              type="text"
              placeholder="Search courses..."
              value={searchQuery}
              onChange={handleSearch}
            />
            <Button variant="success" className="ml-2">
              Search
            </Button>
          </Col>
        </Row>

        {/* Loading State */}
        {isLoading && (
          <div className="text-center">
            <div className="spinner-border text-success" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center text-danger">
            <p>{error}</p>
          </div>
        )}

        {/* Courses Cards */}
        <Row>
          {filteredCourses.slice(indexOfFirstCourse, indexOfLastCourse).map((course) => (
            <Col key={course.course_id} xs={12} sm={6} md={4} lg={3}>
              <Card
                className="mb-4 shadow-lg rounded-3"
                style={{
                  transform: "scale(1)",
                  transition: "transform 0.3s ease",
                }}
              >
                <Card.Body style={{ minHeight: "5rem" }}>
                  <Card.Title className="text-success ">{course.name}</Card.Title>
                  <Card.Text
                    style={{
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      margin: "1rem 0rem",
                    }}
                  >
                    {course.description}
                  </Card.Text>
                  <div className="d-flex justify-content-evenly">
                    <Button
                      variant="outline-success"
                      className="mr-2"
                      onClick={() => handleViewCourse(course)} // Open the view modal
                    >
                      View
                    </Button>
                    <Button
                      variant="outline-success"
                      className="mr-2"
                      onClick={() => handleEditCourse(course.course_id)}
                    >
                      Edit
                    </Button>
                    <Button variant="outline-danger" onClick={() => handleDeleteCourse(course.course_id)}>
                      Delete
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>

        {/* Pagination */}
        <Row className="mt-4 justify-content-center">
          <Col xs={12} className="text-center">
            <ReactPaginate
              previousLabel={"Previous"}
              nextLabel={"Next"}
              pageCount={Math.ceil(filteredCourses.length / coursesPerPage)}
              onPageChange={handlePageChange}
              containerClassName={"pagination justify-content-center"}
              pageClassName={"page-item"}
              previousClassName={"page-item"}
              nextClassName={"page-item"}
              disabledClassName={"disabled"}
              activeClassName={"active"}
              pageLinkClassName={"page-link shadow-sm border-0 rounded-pill"}
              previousLinkClassName={"page-link shadow-sm border-0 rounded-pill"}
              nextLinkClassName={"page-link shadow-sm border-0 rounded-pill"}
              breakClassName={"break-me"}
            />
          </Col>
        </Row>

        {/* Add Course Button */}
        <Link to="/add_courses">
          <Button
            className="position-fixed bottom-0 end-0 m-3"
            style={{
              backgroundColor: "#28a745",
              borderRadius: "50%",
              width: "70px",
              height: "70px",
              fontSize: "2rem",
              padding: "0",
            }}
            variant="success"
          >
            <FaPlusCircle style={{ color: "white" }} />
          </Button>
        </Link>

        {/* Edit Course Modal */}
        <Modal show={showEditModal} onHide={() => setShowEditModal(false)} centered>
          <Modal.Header closeButton>
            <Modal.Title>Edit Course</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form>
              <Form.Group controlId="courseName">
                <Form.Label>Course Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter course name"
                  value={courseData.name}
                  onChange={(e) => setCourseData({ ...courseData, name: e.target.value })}
                />
              </Form.Group>
              <Form.Group controlId="courseDescription">
                <Form.Label>Course Description</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter course description"
                  value={courseData.description}
                  onChange={(e) => setCourseData({ ...courseData, description: e.target.value })}
                />
              </Form.Group>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline-success" onClick={() => setShowEditModal(false)}>
              Close
            </Button>
            <Button variant="success" onClick={handleUpdateCourse}>
              Save Changes
            </Button>
          </Modal.Footer>
        </Modal>

        {/* View Course Modal */}
        <Modal show={showViewModal} onHide={() => setShowViewModal(false)} centered>
          <Modal.Header closeButton>
            <Modal.Title>View Course</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            {selectedCourse && (
              <div>
                <h5>{selectedCourse.name}</h5>
                <p>{selectedCourse.description}</p>
                <p><strong>Course ID:</strong> {selectedCourse.course_id}</p>
              </div>
            )}
          </Modal.Body>
          <Modal.Footer>
            <Button variant="outline-success" onClick={() => setShowViewModal(false)}>
              Close
            </Button>
          </Modal.Footer>
        </Modal>
      </Container>
    </>
  );
};

export default CoursesDashboard;
