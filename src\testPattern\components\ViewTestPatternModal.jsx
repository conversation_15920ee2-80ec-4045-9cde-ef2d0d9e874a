import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, ListGroup, Row, Col } from "react-bootstrap";

const ViewTestPatternModal = ({ show, onHide, pattern }) => {
  if (!pattern) return null;

  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton>
        <Modal.Title>Test Pattern Details</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Card>
          <Card.Body>
            <Row className="mb-4">
              <Col sm={6}>
                <h5 className="text-primary">{pattern.name}</h5>
                {/* <p className="text-muted">{pattern.description}</p> */}
              </Col>
            </Row>

            <Row>
              <Col sm={6}>
                <ListGroup variant="flush">
                  <ListGroup.Item><strong>Version:</strong> {pattern.version}</ListGroup.Item>
                  {/* <ListGroup.Item>
                    <strong>Random Topic:</strong> {pattern.random_topic ? "Yes" : "No"}
                  </ListGroup.Item> */}
                  <ListGroup.Item>
                    <strong>Updated At:</strong> {new Date(pattern.updated_at).toLocaleString()}
                  </ListGroup.Item>
                </ListGroup>
              </Col>
              <Col sm={6}>
                <ListGroup variant="flush">
                  <ListGroup.Item><strong>Positive Mark:</strong> {pattern.positive_mark}</ListGroup.Item>
                  <ListGroup.Item><strong>Negative Mark:</strong> {pattern.negative_marking}</ListGroup.Item>
                  <ListGroup.Item><strong>Contributor ID:</strong> {pattern.contributor}</ListGroup.Item>
                </ListGroup>
              </Col>
            </Row>

            <div className="mt-4">
            </div>

            <div className="mt-4">
              <strong>Sections:</strong>
              {pattern.sections.map((section, index) => (
                <Row key={index} className="mb-3">
                  <Col sm={3}><h6>{section.section_name}</h6> </Col>
                  <Col sm={3}><p><strong>Subjects:</strong>{section.subject_names}</p></Col>
                  <Col sm={3}><p>Time Limit: {section.time_limit} min</p></Col>
                  <Col sm={3}><p>Questions: {section.number_of_questions}</p></Col>
                </Row>
              ))}
            </div>
          </Card.Body>
        </Card>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ViewTestPatternModal;