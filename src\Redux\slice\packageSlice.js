import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Async thunks (for API calls)
export const createPackage = createAsyncThunk('packages/createPackage', async (packageData, thunkAPI) => {
  const response = await fetch('/api/packages', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(packageData),
  });
  return response.json();
});

export const getPackages = createAsyncThunk('packages/getPackages', async () => {
  const response = await fetch('/api/packages');
  return response.json();
});

export const subscribeToPackage = createAsyncThunk('packages/subscribeToPackage', async (packageId, thunkAPI) => {
  const response = await fetch(`/api/packages/subscribe/${packageId}`, {
    method: 'POST',
  });
  return response.json();
});

export const seeSubscription = createAsyncThunk('packages/seeSubscription', async () => {
  const response = await fetch('/api/packages/subscription');
  return response.json();
});

// Initial state
const initialState = {
  packages: [],
  subscription: null,
  status: 'idle', // idle, loading, succeeded, failed
  error: null,
};

// Package slice
const packageSlice = createSlice({
  name: 'packages',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getPackages.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(getPackages.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.packages = action.payload;
      })
      .addCase(getPackages.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })

      .addCase(createPackage.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(createPackage.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.packages.push(action.payload);
      })
      .addCase(createPackage.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })

      .addCase(subscribeToPackage.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(subscribeToPackage.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.subscription = action.payload;
      })
      .addCase(subscribeToPackage.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })

      .addCase(seeSubscription.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(seeSubscription.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.subscription = action.payload;
      })
      .addCase(seeSubscription.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      });
  },
});

// Reducer export
export default packageSlice.reducer;
