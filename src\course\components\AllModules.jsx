import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, Link } from "react-router-dom";
import { Container, Row, Col, Card, Button, Form, Pagination, Modal, Dropdown } from "react-bootstrap";
import { deleteModule, updateModule } from "../../redux/slice/moduleSlice";
import { getSection } from "../../redux/slice/sectionSlice";
import { getTestPatterns } from "../../redux/slice/paperEngineSlice";
import ViewModal from "../../commonComponents/ViewModal";
import RichTextEditor from "../../commonComponents/RichTextEditor";

import { BsPencilSquare, BsTrash } from "react-icons/bs";
import toast, { Toaster } from "react-hot-toast";
import Swal from "sweetalert2";

const AllModules = ({ moduleAdded }) => {
  const dispatch = useDispatch();
  const { sectionSlug } = useParams();

  const { testPatterns, status: testPatternsStatus } = useSelector(
    (state) => state.paperEngine
  );

  const [modules, setModules] = useState([]);
  const [paperName, setpaperName] = useState("");
  const [sectionName, setSectionName] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [modulesPerPage, setModulesPerPage] = useState(6); // Default to 6 per page
  const [selectedPerPage, setSelectedPerPage] = useState("5 per page"); // Default dropdown label
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedModule, setSelectedModule] = useState(null);
  const [editData, setEditData] = useState({
    name: "",
    description: "",
    category: "",
    testPattern: "",
  });

  useEffect(() => {
    if (testPatternsStatus === "pattern_idle") {
      dispatch(getTestPatterns()); // Fetch test patterns if not already fetched
    }
  }, [dispatch, testPatternsStatus]);

  useEffect(() => {
    const fetchModules = async () => {
      try {
        const response = await dispatch(getSection(sectionSlug)).unwrap();
        setModules(response.modules || []);
        setpaperName(response.paper_name);
        setSectionName(response.name);
      } catch (error) {
        toast.error("Failed to fetch modules. Please try again.");
      }
    };

    fetchModules();
  }, [dispatch, moduleAdded, sectionSlug]);

  const filteredModules = modules.filter((module) =>
    (module.name && module.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (module.description && module.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const indexOfLastModule = currentPage * modulesPerPage;
  const indexOfFirstModule = indexOfLastModule - modulesPerPage;
  const currentModules = filteredModules.slice(indexOfFirstModule, indexOfLastModule);

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handlePerPageChange = (value, label) => {
    setModulesPerPage(value);
    setSelectedPerPage(label);
    setCurrentPage(1); // Reset to the first page
  };

  // for view modal

  const handleViewModule = (module) => {
    setSelectedModule(module);
    setShowViewModal(true);
  };

  const handleDeleteModule = async (slug) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deleteModule(slug)).unwrap();
        toast.success("Module deleted successfully!");
      } catch (error) {
        toast.error("Failed to delete the module. Please try again.");
      }
    }
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const openEditModal = (module) => {
    setSelectedModule(module);
    setEditData({
      name: module.name,
      description: module.description,
      category: module.category || "",
      testPattern: module.test_pattern_details.pattern_id || "", // Use pattern_id to set the test pattern
    });
    setShowEditModal(true);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setSelectedModule(null);
    setEditData({ name: "", description: "", category: "", testPattern: "" });
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handleTestPatternChange = (e) => {
    setEditData((prevData) => ({
      ...prevData,
      testPattern: e.target.value,
    }));
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();

    // Prepare the data to be sent to the backend
    const editedData = {
      name: editData.name, // The updated name of the module
      test_pattern: editData.testPattern, // The selected test pattern ID
      description: editData.description, // The updated description
    };

    try {
      await dispatch(updateModule({ slug: selectedModule.slug, data: editedData })).unwrap();
      toast.success("Module updated successfully!");
      setShowEditModal(false);

      // Update the state to reflect the changes in the UI
      setModules((prevModules) =>
        prevModules.map((module) =>
          module.slug === selectedModule.slug ? { ...module, ...editData } : module
        )
      );
    } catch (error) {
      toast.error("Failed to update the module. Please try again.");
    }
  };

  return (
    <Container>
      <Row className="justify-content-center">
        <Col>
          <h5 className="text-center text-success my-3 h5">
            Course / Sub Course / Tier / {paperName} / {sectionName} / Module
          </h5>

          {/* Search Bar */}
          <Form.Group className="mb-4 d-flex align-items-center">
            <Form.Control
              type="text"
              placeholder="Search modules..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="me-2"
            />
            <Dropdown>
              <Dropdown.Toggle variant="success" id="dropdown-basic">
                {selectedPerPage}
              </Dropdown.Toggle>
              <Dropdown.Menu>
                <Dropdown.Item onClick={() => handlePerPageChange(5, "5 per page")}>5 per page</Dropdown.Item>
                <Dropdown.Item onClick={() => handlePerPageChange(25, "25 per page")}>25 per page</Dropdown.Item>
                <Dropdown.Item onClick={() => handlePerPageChange(50, "50 per page")}>50 per page</Dropdown.Item>
                <Dropdown.Item onClick={() => handlePerPageChange(100, "100 per page")}>100 per page</Dropdown.Item>
                <Dropdown.Item onClick={() => handlePerPageChange(filteredModules.length, "All")}>All</Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          </Form.Group>

          {/* Display Modules */}
          <Row>
            {currentModules.length > 0 ? (
              currentModules.map((module) => {
                {/* console.log("MODULE", module) */ }
                {/* console.log("Module Test Pattern ID:", module.test_pattern_details.pattern_id); // Log the testPattern ID of each module */ }

                // Log the entire testPatterns array
                {/* console.log("Test Patterns from store:", testPatterns); */ }

                const associatedPattern = testPatterns.find(
                  (pattern) => pattern.pattern_id === module.test_pattern_details.pattern_id
                );

                // Log the result of the find operation
                console.log(
                  "Associated Test Pattern Name:",
                  associatedPattern ? associatedPattern.name : "N/A"
                );

                return (
                  <Col key={module.slug} xs={12} sm={6} md={6} lg={4}>
                    <Card className="mb-4 shadow-sm rounded-3">
                      <Card.Body>
                        <Card.Title className="text-success text-truncate w-100">{module.name}</Card.Title>
                        <Card.Text className="text-truncate w-100">{module.description}</Card.Text>
                        {/* Show associated test pattern name in the card */}
                        <Card.Text>
                          <strong>Test Pattern:</strong>{" "}
                          {associatedPattern ? associatedPattern.name : "N/A"}
                        </Card.Text>
                        <div className="d-flex flex-wrap justify-content-center mt-2">
                          <Button variant="outline-info" className="m-1 fs-6" onClick={() => handleViewModule(module)}>
                            View
                          </Button>
                          <Button
                            variant="outline-success" className="m-1 fs-6"
                            onClick={() => openEditModal(module)}
                          >
                            Edit
                          </Button>
                          <Button
                            variant="outline-danger" className="m-1 fs-6"
                            onClick={() => handleDeleteModule(module.slug)}
                          >
                            Del
                          </Button>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                );
              })
            ) : (
              <div className="text-center mt-5">
                <div className="spinner-border text-success" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
              </div>
            )}
          </Row>

          {/* Pagination */}
          {filteredModules.length > modulesPerPage && (
            <div className="d-flex justify-content-center mt-4">
              <Pagination>
                {Array.from({ length: Math.ceil(filteredModules.length / modulesPerPage) }).map(
                  (_, index) => (
                    <Pagination.Item
                      key={index + 1}
                      active={index + 1 === currentPage}
                      onClick={() => handlePageChange(index + 1)}
                    >
                      {index + 1}
                    </Pagination.Item>
                  )
                )}
              </Pagination>
            </div>
          )}
        </Col>
      </Row>

      {/* Edit Modal */}
      <Modal show={showEditModal} onHide={handleCloseEditModal} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Module</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleEditSubmit}>
            <Form.Group className="mb-3">
              <RichTextEditor
                label="Module Name"
                name="name"
                value={editData.name}
                onChange={handleEditChange}
                placeholder="Enter module name"
                rows={2}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <RichTextEditor
                label="Description"
                name="description"
                value={editData.description}
                onChange={handleEditChange}
                placeholder="Enter module description"
                rows={3}
                required
              />
            </Form.Group>

            {/* Test Pattern Dropdown */}
            <Form.Group className="mb-3">
              <Form.Label>Test Pattern</Form.Label>
              <Form.Control
                as="select"
                name="testPattern"
                value={editData.testPattern}
                onChange={handleTestPatternChange}
                required
              >
                <option value="">Select Test Pattern</option>
                {testPatterns && testPatterns.length > 0 ? (
                  testPatterns.map((pattern) => (
                    <option key={pattern.pattern_id} value={pattern.pattern_id}>
                      {pattern.name}
                    </option>
                  ))
                ) : (
                  <option>No Test Patterns available</option>
                )}
              </Form.Control>
            </Form.Group>

            <div className="text-center">
              <Button type="submit" variant="outline-success">
                Save Changes
              </Button>
            </div>
          </Form>
        </Modal.Body>
      </Modal>

      <ViewModal show={showViewModal} onHide={() => setShowViewModal(false)} content={selectedModule} />

      {/* Toaster for notifications */}
      <Toaster />
    </Container>
  );
};

export default AllModules;
