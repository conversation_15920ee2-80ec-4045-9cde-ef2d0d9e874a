import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor;
  return accessToken;
};

// Create Subject Thunk
export const createSubject = createAsyncThunk(
  'subject/createSubject',
  async (subjectData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CREATE_SUBJECT}`,
        subjectData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error creating subject');
    }
  }
);

// Get Subjects Thunk
export const getSubjects = createAsyncThunk(
  'subject/getSubjects',
  async (_, { getState, rejectWithValue }) => {
    try {
      // const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_SUBJECTS}`,
        // {
        //   headers: {
        //     Authorization: `Bearer ${token}`,
        //   },
        // }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching subjects');
    }
  }
);

// Get Single Subject Thunk
export const getSubject = createAsyncThunk(
  'subject/getSubject',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_SUBJECT}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching subject');
    }
  }
);

// Update Subject Thunk
export const updateSubject = createAsyncThunk(
  'subject/updateSubject',
  async ({ slug, updatedData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_UPDATE_SUBJECT}${slug}/`,
        updatedData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error updating subject');
    }
  }
);

// Delete Subject Thunk
export const deleteSubject = createAsyncThunk(
  'subject/deleteSubject',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_DELETE_SUBJECT}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return { slug }; // Return only slug in the payload
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error deleting subject');
    }
  }
);

const subjectSlice = createSlice({
  name: 'subject',
  initialState: {
    subject: [],
    subjects: [],
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Create Subject
      .addCase(createSubject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createSubject.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subject = action.payload;
      })
      .addCase(createSubject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Get Subjects
      .addCase(getSubjects.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSubjects.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subjects = action.payload.data; // Fix to directly access the subjects array
      })
      .addCase(getSubjects.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Get Single Subject
      .addCase(getSubject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSubject.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subject = action.payload;
      })
      .addCase(getSubject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Update Subject
      .addCase(updateSubject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateSubject.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subject = action.payload;
      })
      .addCase(updateSubject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Delete Subject
      .addCase(deleteSubject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteSubject.fulfilled, (state, action) => {
        state.isLoading = false;
        // Filter out the deleted subject from the subjects data
        state.subjects = state.subjects.filter(
          (subject) => subject.slug !== action.payload.slug
        );
      })
      .addCase(deleteSubject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default subjectSlice.reducer;
