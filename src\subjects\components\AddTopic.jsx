import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { createTopic } from "../../redux/slice/topicsSlice"; // Redux action to create a topic
import { getSubjects } from "../../redux/slice/subjectSlice"; // Redux action to fetch subjects
import { Button, Form, Card, Spinner } from "react-bootstrap";

const AddTopic = ({ slug, onTopicAdded }) => {
  const dispatch = useDispatch();
  const { subjects, status: subjectStatus } = useSelector((state) => state.subject); // Access subjects from Redux store

  const [topicName, setTopicName] = useState("");
  const [topicDescription, setTopicDescription] = useState("");

  // Fetch subjects when the component mounts
  useEffect(() => {
    if (subjectStatus === "idle") {
      dispatch(getSubjects());
    }
  }, [dispatch, subjectStatus]);

  const handleAddTopic = (event) => {
    event.preventDefault(); // Prevent default form submission
    if (topicName && topicDescription) {
      dispatch(
        createTopic({
          topicData: {
            subject: slug,         
            name: topicName,
            description: topicDescription,
          }
            
        })
      ).then(() => {
        // Trigger the callback to refresh the list
        onTopicAdded();

        // Reset the form
        setTopicName("");
        setTopicDescription("");
      });
    } else {
      alert("Please provide all fields for the topic.");
    }
  };

  return (
    <div className="add-topic-container" >
      <Card className="mt-5 shadow-lg rounded-3">
        <Card.Body>
          <Card.Title className="text-center text-success">Add Topic</Card.Title>
          <Form onSubmit={handleAddTopic}>
            <Form.Group controlId="topicName">
              <Form.Label>Topic Name</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter topic name"
                value={topicName}
                onChange={(e) => setTopicName(e.target.value)}
              />
            </Form.Group>

            <Form.Group controlId="topicDescription" className="mt-2">
              <Form.Label>Topic Description</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter topic description"
                value={topicDescription}
                onChange={(e) => setTopicDescription(e.target.value)}
              />
            </Form.Group>

            <Button variant="success" className="w-100 mt-1" type="submit">
              Add Topic
            </Button>
          </Form>
        </Card.Body>
      </Card>

      <div className="d-flex justify-content-center align-items-center">
          <Link to="/contributor_dashboard">
            <Button variant="secondary" className="mt-5 text-center">
              Back to Dashboard
            </Button>
          </Link>
      </div>
    </div>
  );
};

export default AddTopic;
