import React, { useEffect, useState } from 'react';
import { Modal, Form, Row, Col, Button } from 'react-bootstrap';
import { toast } from 'react-hot-toast';
import { useDispatch, useSelector } from 'react-redux';
import { getCourses } from '../redux/slice/courseSlice'; // Update path as needed

const PreviousYearQuestionModal = ({
  showPrevYearModal,
  setShowPrevYearModal,
  prevYearQuestionData,
  setPrevYearQuestionData,
  handlePrevYearSubmit,
}) => {
  const dispatch = useDispatch();
  const [courses, setCourses] = useState([]);
  const [subCourses, setSubCourses] = useState([]);

  // Fetch courses on mount
  useEffect(() => {
    dispatch(getCourses())
      .unwrap()
      .then((response) => {
        setCourses(response);
      })
      .catch((error) => {
        console.error("Error fetching courses:", error);
        toast.error("Failed to fetch courses.");
      });
  }, [dispatch]);

  // Handle course change
  const handleCourseChange = (e) => {
    const selectedCourseId = e.target.value;
    setPrevYearQuestionData({
      ...prevYearQuestionData,
      course: selectedCourseId,
      exams: "", // Reset exams when course changes
    });

    // Find selected course and update subcourses (exams)
    const selectedCourse = courses.find((course) => course.course_id === parseInt(selectedCourseId));
    setSubCourses(selectedCourse?.sub_courses || []);
  };

  // Handle exam change
  const handleExamChange = (e) => {
    setPrevYearQuestionData({
      ...prevYearQuestionData,
      exams: e.target.value,
    });
  };

  // Validate and restrict year/month inputs
  const handleYearChange = (e) => {
    const inputYear = e.target.value;
    const currentYear = new Date().getFullYear();
    if (inputYear <= currentYear) {
      setPrevYearQuestionData({ ...prevYearQuestionData, year: inputYear });
    } else {
      toast.error("Year cannot be in the future.");
    }
  };

  const handleMonthChange = (e) => {
    const inputMonth = e.target.value;
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // Months are 0-indexed
    if (
      prevYearQuestionData.year < currentYear ||
      (prevYearQuestionData.year === currentYear && inputMonth <= currentMonth)
    ) {
      setPrevYearQuestionData({ ...prevYearQuestionData, month: inputMonth });
    } else {
      toast.error("Month cannot be in the future.");
    }
  };

  return (
    <Modal show={showPrevYearModal} onHide={() => setShowPrevYearModal(false)} centered>
      <Modal.Header closeButton>
        <Modal.Title>Mark as Previous Year Question</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handlePrevYearSubmit}>
          <Row>
            <Col>
              <Form.Group controlId="questionId" className="d-none">
                <Form.Label>Question ID</Form.Label>
                <Form.Control
                  type="text"
                  value={prevYearQuestionData.question || ""}
                  onChange={(e) =>
                    setPrevYearQuestionData({
                      ...prevYearQuestionData,
                      question: e.target.value,
                    })
                  }
                />
              </Form.Group>
            </Col>
            <Col md={12}>
              <Form.Group controlId="year">
                <Form.Label>Year</Form.Label>
                <Form.Control
                  type="number"
                  value={prevYearQuestionData.year || ""}
                  onChange={handleYearChange}
                />
              </Form.Group>
            </Col>
          </Row>
          <Row>
            <Col md={6}>
              <Form.Group controlId="month">
                <Form.Label>Month</Form.Label>
                <Form.Control
                  type="number"
                  min={1}
                  max={12}
                  value={prevYearQuestionData.month || ""}
                  onChange={handleMonthChange}
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group controlId="course">
                <Form.Label>Course</Form.Label>
                <Form.Control
                  as="select"
                  value={prevYearQuestionData.course || ""}
                  onChange={handleCourseChange}
                >
                  <option value="">-- Select a Course --</option>
                  {courses.map((course) => (
                    <option key={course.course_id} value={course.course_id}>
                      {course.name}
                    </option>
                  ))}
                </Form.Control>
              </Form.Group>
            </Col>
          </Row>
          <Row>
            <Col md={6}>
              <Form.Group controlId="exams">
                <Form.Label>Exam</Form.Label>
                <Form.Control
                  as="select"
                  value={prevYearQuestionData.exams || ""}
                  onChange={handleExamChange}
                  disabled={!subCourses.length}
                >
                  <option value="">-- Select an Exam --</option>
                  {subCourses.map((exam) => (
                    <option key={exam.subcourse_id} value={exam.subcourse_id}>
                      {exam.name}
                    </option>
                  ))}
                </Form.Control>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group controlId="status">
                <Form.Label>Status</Form.Label>
                <Form.Control
                  as="select"
                  value={prevYearQuestionData.status || ""}
                  onChange={(e) =>
                    setPrevYearQuestionData({
                      ...prevYearQuestionData,
                      status: e.target.value,
                    })
                  }
                >
                  <option value="">-- Select Status --</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </Form.Control>
              </Form.Group>
            </Col>
          </Row>
          <Row>
            <Col md={12}>
              <Form.Group controlId="note">
                <Form.Label>Note</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  value={prevYearQuestionData.note || ""}
                  onChange={(e) =>
                    setPrevYearQuestionData({
                      ...prevYearQuestionData,
                      note: e.target.value,
                    })
                  }
                />
              </Form.Group>
            </Col>
          </Row>
          <Button variant="outline-success" type="submit" className="w-100 mt-2">
            Submit
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default PreviousYearQuestionModal;
