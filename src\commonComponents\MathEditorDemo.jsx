import React, { useState } from 'react';
import { Contain<PERSON>, <PERSON>, Col, <PERSON>, But<PERSON>, Alert, Form } from 'react-bootstrap';
import MathEditor from './MathEditor';
import MathTextRenderer, { containsMath, extractMathExpressions } from './MathTextRenderer';

const MathEditorDemo = () => {
  const [questionText, setQuestionText] = useState('What is the solution to the quadratic equation?');
  const [optionText, setOptionText] = useState('The answer is');
  const [mathContent, setMathContent] = useState('');
  const [showMathEditor, setShowMathEditor] = useState(false);
  const [savedData, setSavedData] = useState(null);
  const [showAlert, setShowAlert] = useState(false);

  const handleSave = () => {
    const data = {
      questionText,
      optionText,
      questionMathExpressions: extractMathExpressions(questionText),
      optionMathExpressions: extractMathExpressions(optionText),
      timestamp: new Date().toISOString()
    };

    // Simulate saving to backend - no backend changes needed!
    console.log('Saving data to backend (same fields as before):', data);
    setSavedData(data);
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  const handleClear = () => {
    setQuestionText('What is the solution to the quadratic equation?');
    setOptionText('The answer is');
    setMathContent('');
    setSavedData(null);
  };

  const loadSample = (latex) => {
    setMathContent(latex);
  };

  const sampleExpressions = [
    { label: 'Quadratic Formula', latex: 'x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}' },
    { label: 'Integral', latex: '\\int_0^1 x^2 dx = \\frac{1}{3}' },
    { label: 'Limit', latex: '\\lim_{x \\to \\infty} \\frac{1}{x} = 0' },
    { label: 'Matrix', latex: '\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}' },
    { label: 'Sum', latex: '\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}' }
  ];

  const sampleTexts = [
    'Find the derivative of $f(x) = x^2 + 3x + 2$',
    'Solve the equation $$x^2 - 5x + 6 = 0$$',
    'The limit $\\lim_{x \\to 0} \\frac{\\sin x}{x} = 1$ is fundamental',
    'Matrix multiplication: $\\begin{pmatrix} 1 & 2 \\\\ 3 & 4 \\end{pmatrix} \\begin{pmatrix} 5 \\\\ 6 \\end{pmatrix}$'
  ];

  return (
    <Container className="mt-4">
      <Row>
        <Col>
          <h2 className="mb-4">Math Editor Demo - No Backend Changes Needed!</h2>

          {showAlert && (
            <Alert variant="success" className="mb-4">
              Content saved successfully! Math is embedded in existing text fields. Check console for details.
            </Alert>
          )}

          {/* Sample Texts with Embedded Math */}
          <Card className="mb-4">
            <Card.Header>
              <h5>Sample Text with Embedded Math (Click to Load)</h5>
            </Card.Header>
            <Card.Body>
              <div className="d-flex flex-wrap gap-2 mb-3">
                {sampleTexts.map((text, index) => (
                  <Button
                    key={index}
                    variant="outline-success"
                    size="sm"
                    onClick={() => setQuestionText(text)}
                  >
                    Sample {index + 1}
                  </Button>
                ))}
              </div>
              <small className="text-muted">
                These examples show how math is embedded directly in text using $ and $$ delimiters
              </small>
            </Card.Body>
          </Card>

          {/* Question Text Editor */}
          <Card className="mb-4">
            <Card.Header>
              <h5>Question Content (with embedded math)</h5>
            </Card.Header>
            <Card.Body>
              <Form.Group className="mb-3">
                <Form.Label>Question Text</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  value={questionText}
                  onChange={(e) => setQuestionText(e.target.value)}
                  placeholder="Enter question text..."
                />
              </Form.Group>

              <div className="d-flex justify-content-between align-items-center mb-2">
                <Form.Label>Add Math to Question</Form.Label>
                <Button
                  variant="outline-primary"
                  size="sm"
                  onClick={() => setShowMathEditor(!showMathEditor)}
                >
                  {showMathEditor ? 'Hide' : 'Show'} Math Editor
                </Button>
              </div>

              {showMathEditor && (
                <MathEditor
                  value={mathContent}
                  onChange={setMathContent}
                  label="Mathematical Expression"
                  placeholder="Enter math expression to insert..."
                  showPreview={true}
                  showRawLatex={false}
                  displayMode={true}
                  embeddedMode={true}
                  textContent={questionText}
                  onTextContentChange={setQuestionText}
                  className="mb-3"
                />
              )}

              <div className="mt-3">
                <Form.Label>Live Preview:</Form.Label>
                <div className="border rounded p-3 bg-light">
                  <MathTextRenderer text={questionText} />
                </div>
                {containsMath(questionText) && (
                  <small className="text-success">
                    ✓ Contains {extractMathExpressions(questionText).length} math expression(s)
                  </small>
                )}
              </div>
            </Card.Body>
          </Card>

          {/* Option Text Editor */}
          <Card className="mb-4">
            <Card.Header>
              <h5>Option Content (with embedded math)</h5>
            </Card.Header>
            <Card.Body>
              <Form.Group className="mb-3">
                <Form.Label>Option Text</Form.Label>
                <Form.Control
                  type="text"
                  value={optionText}
                  onChange={(e) => setOptionText(e.target.value)}
                  placeholder="Enter option text..."
                />
              </Form.Group>

              <div className="mt-3">
                <Form.Label>Option Preview:</Form.Label>
                <div className="border rounded p-2 bg-light">
                  <MathTextRenderer text={optionText} />
                </div>
              </div>
            </Card.Body>
          </Card>

          {/* Action Buttons */}
          <div className="d-flex gap-2 mb-4">
            <Button
              variant="primary"
              onClick={handleSave}
              disabled={!questionText && !optionText}
            >
              Save Content (No Backend Changes!)
            </Button>
            <Button
              variant="outline-secondary"
              onClick={handleClear}
            >
              Reset Demo
            </Button>
          </div>

          {/* Saved Data Display */}
          {savedData && (
            <Card>
              <Card.Header>
                <h5>Saved Data - Same Database Fields as Before!</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <h6>Question Content:</h6>
                    <div className="mb-2">
                      <MathTextRenderer text={savedData.questionText} />
                    </div>
                    <small className="text-muted">
                      <strong>Raw Text:</strong> <code>{savedData.questionText}</code>
                    </small>
                    {savedData.questionMathExpressions.length > 0 && (
                      <div className="mt-2">
                        <small className="text-success">
                          <strong>Math Expressions Found:</strong> {savedData.questionMathExpressions.length}
                        </small>
                        <ul className="small">
                          {savedData.questionMathExpressions.map((expr, i) => (
                            <li key={i}>{expr.type}: <code>{expr.latex}</code></li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </Col>
                  <Col md={6}>
                    <h6>Option Content:</h6>
                    <div className="mb-2">
                      <MathTextRenderer text={savedData.optionText} />
                    </div>
                    <small className="text-muted">
                      <strong>Raw Text:</strong> <code>{savedData.optionText}</code>
                    </small>
                    {savedData.optionMathExpressions.length > 0 && (
                      <div className="mt-2">
                        <small className="text-success">
                          <strong>Math Expressions Found:</strong> {savedData.optionMathExpressions.length}
                        </small>
                        <ul className="small">
                          {savedData.optionMathExpressions.map((expr, i) => (
                            <li key={i}>{expr.type}: <code>{expr.latex}</code></li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </Col>
                </Row>
                <hr />
                <Alert variant="info" className="mb-0">
                  <small>
                    <strong>✓ No Backend Changes Required!</strong><br/>
                    Math content is embedded in existing text fields using $ and $$ delimiters.<br/>
                    The same database schema works - just render with MathTextRenderer on display.
                  </small>
                </Alert>
                <small className="text-muted d-block mt-2">
                  <strong>Saved at:</strong> {new Date(savedData.timestamp).toLocaleString()}
                </small>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default MathEditorDemo;
