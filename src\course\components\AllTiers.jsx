import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { usePara<PERSON>, <PERSON> } from "react-router-dom";
import { Container, Row, Col, Card, Button, Form, Pagination, Modal, Dropdown, DropdownButton } from "react-bootstrap";
import { deleteTier, updateTier } from "../../redux/slice/tierSlice"; // Replace with your Redux slice paths
import { getSubCourse } from "../../redux/slice/subCourseSlice";
import { getTestPatterns } from "../../redux/slice/paperEngineSlice"; // Redux action to fetch test patterns
import ViewModal from "../../commonComponents/ViewModal";

import { BsPencilSquare, BsTrash } from "react-icons/bs";
import toast, { Toaster } from "react-hot-toast";
import Swal from "sweetalert2";



const AllTiers = ({ tierAdded }) => {
  const dispatch = useDispatch();
  const { subcourseSlug } = useParams();

  const [tiers, setTiers] = useState([]);
  const [subCourseName, SetSubCourseName] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [tiersPerPage, setTiersPerPage] = useState(6); // Default to 6 per page
  const [selectedPerPage, setSelectedPerPage] = useState("6 per page"); // Default label
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedTier, setSelectedTier] = useState(null);
  const [editData, setEditData] = useState({ name: "", description: "", test_pattern: "" });
  const [testPatterns, setTestPatterns] = useState([]); // Store test patterns

  const fetchTestPatterns = async () => {
    try {
      const response = await dispatch(getTestPatterns()).unwrap();
      setTestPatterns(response); // Assuming response contains the test patterns
    } catch (error) {
      toast.error("Failed to fetch test patterns.");
    }
  };

  const fetchTiers = async () => {
    try {
      const response = await dispatch(getSubCourse(subcourseSlug)).unwrap();
      // Extracting the tiers from the response
      setTiers(response.tiers);
      SetSubCourseName(response.name);
    } catch (error) {
      toast.error("Failed to fetch tiers. Please try again.");
    }
  };

  // Fetch test patterns when the component mounts
  useEffect(() => {
    fetchTestPatterns();
  }, [dispatch]);

  // Re-fetch tiers when tierAdded changes or on component mount
  useEffect(() => {
    fetchTiers();
  }, [dispatch, tierAdded, subcourseSlug]);

  const filteredTiers = tiers.filter((tier) =>
    (tier.name && tier.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (tier.description && tier.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );


  const indexOfLastTier = currentPage * tiersPerPage;
  const indexOfFirstTier = indexOfLastTier - tiersPerPage;
  const currentTiers = filteredTiers.slice(indexOfFirstTier, indexOfLastTier);

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handlePerPageChange = (value, label) => {
    setTiersPerPage(value);
    setSelectedPerPage(label);
    setCurrentPage(1); // Reset to the first page
  };

  // for view modal

  const handleViewTier = (tier) => {
    setSelectedTier(tier);
    setShowViewModal(true);
  };

  const handleDeleteTier = async (slug) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deleteTier(slug));
        fetchTiers();
        toast.success("Tier deleted successfully!");
      } catch (error) {
        toast.error("Failed to delete the tier. Please try again.");
      }
    }
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Handle View Modal Open
  const handleViewModalOpen = (tier) => {
    setSelectedTier(tier);
    setShowViewModal(true);
  };

  const handleCloseViewModal = () => {
    setShowViewModal(false);
    setSelectedTier(null);
  };

  // Handle Edit Modal Open
  const openEditModal = (tier) => {
    setSelectedTier(tier);
    setEditData({
      name: tier.name,
      description: tier.description,
      test_pattern: tier.test_pattern_details ? tier.test_pattern_details.pattern_id : "none" // Ensure 'none' if no pattern
    });
    setShowEditModal(true);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setSelectedTier(null);
    setEditData({ name: "", description: "", test_pattern: "none" });
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handleEditSubmit = async (e) => {
    e.preventDefault();

    // Construct the data payload dynamically
    const { test_pattern, ...otherData } = editData;
    const payload = test_pattern !== "none" ? { ...otherData, test_pattern } : otherData; // Do not include test_pattern if "none" is selected

    try {
      await dispatch(updateTier({ slug: selectedTier.slug, data: payload })).unwrap();
      toast.success("Tier updated successfully!");
      setShowEditModal(false);
      fetchTiers(); // Re-fetch tiers after the update
    } catch (error) {
      toast.error("Failed to update the tier. Please try again.");
    }
  };

  return (
    <Container>
      <Row className="justify-content-center">
        <Col>
          <h5 className="text-center text-success my-3 h5"> {subCourseName} / Tiers</h5>

          {/* Search Bar and Per Page Dropdown */}
          <div className="d-flex justify-content-between align-items-center mb-3">
            <Form.Group className="mb-0 flex-grow-1 me-2">
              <Form.Control
                type="text"
                placeholder="Search tiers..."
                value={searchQuery}
                onChange={handleSearchChange}
              />
            </Form.Group>
            <DropdownButton
              id="dropdown-per-page"
              title={selectedPerPage}
              variant="success"
              className="ms-2"
            >
              <Dropdown.Item onClick={() => handlePerPageChange(5, "5 per page")}>5 per page</Dropdown.Item>
              <Dropdown.Item onClick={() => handlePerPageChange(25, "25 per page")}>25 per page</Dropdown.Item>
              <Dropdown.Item onClick={() => handlePerPageChange(50, "50 per page")}>50 per page</Dropdown.Item>
              <Dropdown.Item onClick={() => handlePerPageChange(100, "100 per page")}>100 per page</Dropdown.Item>
              <Dropdown.Item onClick={() => handlePerPageChange(filteredTiers.length, "All")}>All</Dropdown.Item>
            </DropdownButton>
          </div>

          {/* Display Tiers */}
          <Row>
            {currentTiers.length > 0 ? (
              currentTiers.map((tier) => (
                <Col key={tier.slug} xs={12} sm={6} md={6} lg={4}>
                  <Card className="mb-4 shadow-sm rounded-3" >
                    <Card.Body>
                      <Card.Title className="text-success text-truncate w-100">{tier.name}</Card.Title>
                      <Card.Text className="text-truncate w-100" >
                        {tier.description}
                      </Card.Text>                      
                      <Card.Text className="mt-2">
                        {tier.test_pattern_details
                          ? tier.test_pattern_details.name || "No test pattern name available"
                          : "No test pattern available"}
                      </Card.Text>
                      <div className="d-flex flex-wrap justify-content-center pt-2">
                        <Link to={`/add_paper/${tier.slug}`}>
                          <Button variant="outline-primary" className="m-1 fs-6">
                            Paper
                          </Button>
                        </Link>
                        <Button variant="outline-info" className="m-1 fs-6" onClick={() => handleViewTier(tier)}>
                          View
                        </Button>
                        <Button
                          variant="outline-success"
                          className="m-1 fs-6"
                          onClick={() => openEditModal(tier)}
                        >
                          <BsPencilSquare/>
                        </Button>

                        <Button
                          variant="outline-danger"
                          className="m-1 fs-6"
                          onClick={() => handleDeleteTier(tier.slug)}
                        >
                          <BsTrash/>
                        </Button>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))
            ) : (
              <div className="col-12 text-center">No tiers found</div>
            )}
          </Row>

          {/* Pagination */}
          <div className="d-flex justify-content-center mt-4">
            <Pagination>
              {Array.from({ length: Math.ceil(filteredTiers.length / tiersPerPage) }).map(
                (_, index) => (
                  <Pagination.Item
                    key={index + 1}
                    active={index + 1 === currentPage}
                    onClick={() => handlePageChange(index + 1)}
                  >
                    {index + 1}
                  </Pagination.Item>
                )
              )}
            </Pagination>
          </div>
        </Col>
      </Row>

      {/* Edit Modal */}
      <Modal show={showEditModal} onHide={handleCloseEditModal} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Tier</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleEditSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Tier Name</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={editData.name}
                onChange={handleEditChange}
                required
              />
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                name="description"
                rows={3}
                value={editData.description}
                onChange={handleEditChange}
                required
              />
            </Form.Group>

            {/* Display current test pattern or 'No test pattern selected' */}
            {editData.test_pattern !== "none" ? (
              <h6 className="my-2">Current Test Pattern: {testPatterns.find((pattern) => pattern.pattern_id === editData.test_pattern)?.name || 'None'}</h6>
            ) : (
              <h6 className="my-2">No test pattern selected</h6>
            )}

            <Form.Group className="mb-3">
              <Form.Label>Test Pattern</Form.Label>
              <Form.Control
                as="select"
                name="test_pattern"
                value={editData.test_pattern}
                onChange={handleEditChange}
              >
                <option value="none">None</option>
                {testPatterns.map((pattern) => (
                  <option key={pattern.pattern_id} value={pattern.pattern_id}>
                    {pattern.name} (Version: {pattern.version})
                  </option>
                ))}
              </Form.Control>
            </Form.Group>

            <Button variant="outline-primary w-100" type="submit">
              Save Changes
            </Button>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseEditModal}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      <ViewModal show={showViewModal} onHide={() => setShowViewModal(false)} content={selectedTier} />

      <Toaster />
    </Container>
  );
};

export default AllTiers;
