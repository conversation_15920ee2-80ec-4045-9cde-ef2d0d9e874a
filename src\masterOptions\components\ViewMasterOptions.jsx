import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {  getAllMasterOptions,  deleteMasterOption,  updateMasterOption,} from "../../redux/slice/masterOptionSlice";
import { deleteOption, updateOption } from "../../redux/slice/optionsSlice";
import {  deleteQuestion,  updateQuestion,} from "../../redux/slice/questionSlice";
import {  Container,  Form,  ButtonGroup,  Button,  Dropdown,  DropdownButton,  Modal,} from "react-bootstrap";

import Swal from "sweetalert2";
import { toast } from "react-hot-toast";
import imageCompression from "browser-image-compression";
import PaginationComponent from "../../commonComponents/PaginationComponent";
import QuestionListSkeleton from "../../commonComponents/QuestionListSkeleton";
import MasterOptionCard from "../../commonComponents/MasterOptionCard";
import QuestionEditModal from "../../commonComponents/QuestionEditModal";
import OptionEditModal from "../../commonComponents/OptionEditModal";
import MasterOptionPassageModal from "../../commonComponents/MasterOptionPassageModal";

export default function ViewMasterOptions({  searchTerm,  onSearchTermChange,  optionAddedFlag,}) {
  const dispatch = useDispatch();
  const contributorProfileId = useSelector(
    (state) => state.contributor.contributorProfileId || null
  );

  const [allOptions, setAllOptions] = useState([]);
  const [filteredOptions, setFilteredOptions] = useState([]);
  const [itemsPerPage, setItemsPerPage] = useState(2);
  const [currentPage, setCurrentPage] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [showQuestionModal, setShowQuestionModal] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState(null);

  const [updatedData, setUpdatedData] = useState({
    author: contributorProfileId,
    title: "",
    option_content: "",
    conditions: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const [filter, setFilter] = useState("all"); // Current filter (all, pending, approved, rejected)

  // Add these new state variables at the top with other state declarations
  const [newImage, setNewImage] = useState(null);
  const [imageError, setImageError] = useState("");
  const [imagePreview, setImagePreview] = useState(null);
  const [imageSizeText, setImageSizeText] = useState("");

  const imageInputRef = useRef(null);

  const handleImageChange = (e) => {
    const file = e.target.files[0];

    if (!file) return;

    // setIsCheckingImage(true);
    setImageError("");
    setNewImage(null);
    setImagePreview(null);

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 200 * 1024) {
      console.log("Image is less than 200kb: ", file);

      // Directly set the image if it is small enough
      setNewImage(file);
      const reader = new FileReader();
      reader.onload = () => setImagePreview(reader.result);
      reader.readAsDataURL(file);
      // setIsCheckingImage(false);
    } else {
      const options = {
        maxSizeMB: 0.2,
        maxWidthOrHeight: 300,
        useWebWorker: true,
      };

      try {
        imageCompression(file, options)
          .then((compressedFile) => {
            const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);
            console.log("Compressed file size:", compressedFile.size); // Debugging

            if (compressedFile.size <= 200 * 1024) {
              // Convert Blob to File (required for form data)
              const fileName = "compressed_" + file.name;
              const compressedFileAsFile = new File(
                [compressedFile],
                fileName,
                {
                  type: compressedFile.type,
                }
              );

              console.log("Setting compressed image:", compressedFileAsFile);

              // Set the image as File object
              setNewImage(compressedFileAsFile);

              const reader = new FileReader();
              reader.onload = () => setImagePreview(reader.result);
              reader.readAsDataURL(compressedFileAsFile);

              // Display the image sizes in the UI
              setImageSizeText(
                `Original Size: ${originalSizeKB} KB, Compressed Size: ${compressedSizeKB} KB`
              );
            } else {
              setImageError(
                `Image exceeds 200KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`
              );
            }
          })
          .catch((error) => {
            console.error("Image compression failed:", error);
            setImageError("An error occurred while compressing the image.");
          })
          .finally(() => {
            // setIsCheckingImage(false);
          });
      } catch (error) {
        console.error("Error handling image change:", error);
        setImageError("An error occurred while processing the image.");
        // setIsCheckingImage(false);
      }
    }
  };

  // Function to fetch master options
  const fetchMasterOptions = async () => {
    setIsLoading(true);
    try {
      const response = await dispatch(getAllMasterOptions());
      if (response && response.payload) {
        const options = response.payload;
        setAllOptions(options);
        filterOptions(options);
      }
    } catch (error) {
      console.error("Error fetching master options:", error);
      toast.error("Failed to load options");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch master options when component mounts or when `optionAddedFlag` changes
  useEffect(() => {
    fetchMasterOptions();
  }, [optionAddedFlag]);

  const filterOptions = (options) => {
    const filtered = options.filter(
      (option) =>
        option.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
        option.is_current_affairs === false
    );
    setFilteredOptions(filtered);
  };

  useEffect(() => {
    filterOptions(allOptions); // Re-filter when searchTerm changes
  }, [searchTerm, allOptions]);

  // Handle filter button clicks
  const handleFilterClick = (newFilter) => {
    setFilter(newFilter);

    let filteredData = allQuestions;

    if (newFilter === "pending") {
      filteredData = allQuestions.filter(
        (q) => q.approval_status === "pending"
      );
    } else if (newFilter === "approved") {
      filteredData = allQuestions.filter(
        (q) => q.approval_status === "approved"
      );
    } else if (newFilter === "rejected") {
      filteredData = allQuestions.filter(
        (q) => q.approval_status === "rejected"
      );
    }

    setFilteredQuestions(filteredData);
  };

  const paginate = (array, pageSize, pageNumber) => {
    const offset = (pageNumber - 1) * pageSize;
    return array.slice(offset, offset + pageSize);
  };

  const handleDropdownChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when items per page changes
  };

  const paginatedOptions = paginate(filteredOptions, itemsPerPage, currentPage);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const totalPages = Math.ceil(filteredOptions?.length / itemsPerPage);

  const handleEdit = (option) => {
    setSelectedOption(option);
    setUpdatedData({
      author: contributorProfileId,
      title: option.title,
      option_content: option.option_content,
      conditions: option.conditions,
    });
    setShowModal(true);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUpdatedData((prevData) => ({
      ...prevData,
      [name]: value,
      author: contributorProfileId,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (
      updatedData.title &&
      updatedData.option_content &&
      updatedData.conditions
    ) {
      try {
        const actionResult = await dispatch(
          updateMasterOption({ slug: selectedOption.slug, updatedData })
        );

        // Check if the action was fulfilled
        if (actionResult.meta.requestStatus === "fulfilled") {
          setShowModal(false);
          // Refetch options after successful update
          fetchMasterOptions();
          toast.success("Option updated successfully!");
        } else {
          // Handle failure case
          const errorMessage =
            actionResult.payload?.error || "Failed to update the option.";
          toast.error(errorMessage);
          console.error("Error response:", actionResult);
        }
      } catch (error) {
        console.error("Error updating option:", error);
        toast.error("An unexpected error occurred while updating the option.");
      }
    } else {
      toast.error("Please fill out all the required fields.");
    }
  };

  const handleDelete = async (slug) => {
    if (!slug) {
      toast.error("ID is missing, cannot delete the item.");
      return;
    }

    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        const actionResult = await dispatch(deleteMasterOption(slug));

        // Check if the action was fulfilled
        if (actionResult.meta.requestStatus === "fulfilled") {
          Swal.fire("Deleted!", "The option has been deleted.", "success");
          // Refetch options after successful deletion
          fetchMasterOptions();
        } else {
          // Handle failure case
          const errorMessage =
            actionResult.payload?.error || "Failed to delete the option.";
          toast.error(errorMessage);
          console.error("Error response:", actionResult);
        }
      } catch (error) {
        console.error("Error deleting the option:", error);
        toast.error("An unexpected error occurred while deleting the option.");
      }
    }
  };

  // States for option edit modal
  const [showOptionModal, setShowOptionModal] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [updatedOptionData, setUpdatedOptionData] = useState({
    option_text: "",
    is_correct: false,
  });
  const [selectedQuestionId, setSelectedQuestionId] = useState(null); // Initialize the selected question ID state

  // state for updaing the question
  const [updatedQuestionData, setQuestionUpdatedQuestionData] = useState({
    content: "",
    difficulty: 3,
    author: contributorProfileId,
    status: "active",
    // current_affairs: null,
    // is_current_affairs: false,
    approval_status: "pending",
    average_score: 0.0,
    times_attempted: 0,
    subject: "",
    subject_name: "",
    topic: "",
    topic_name: "",
    sub_topic: "",
    sub_topic_name: "",
    language: "",
    course: [],
    subcourse: [],
    // master_question: null,
    // is_master: false,
    // master_option: null,
    // is_master_option: false,
    options: [],
  });

  // FUNCTION FOR EDIT THE QUESTION

  const handleQuestionInputChange = (e) => {
    const { name, value } = e.target;
    setQuestionUpdatedQuestionData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  // FUNCTION FOR EDIT THE QUESTION

  const handleEditQuestion = (question) => {
    setSelectedQuestion(question);
    setQuestionUpdatedQuestionData({
      content: question.content, // Only the question content is editable
      subject: question.subject, // Pre-set subject, topic, and subtopic
      subject_name: question.subject_name,
      topic: question.topic,
      topic_name: question.topic_name,
      sub_topic: question.sub_topic,
      sub_topic_name: question.sub_topic_name,
    });
    // Set initial image preview if question has attachment
    setImagePreview(
      question.attachments
        ? `${import.meta.env.VITE_BASE_URL}/${question.attachments}`
        : null
    );
    setNewImage(null); // Reset new image state
    console.log("SetNewImage is set to null");
    setShowQuestionModal(true); // Show modal for editing
  };

  const handleSubmitQuestion = async (e) => {
    e.preventDefault();

    try {
      const payload = {
        content: updatedQuestionData.content, // Only update the content
        subject: selectedQuestion.subject, // Keep the original subject
        subject_name: selectedQuestion.subject_name, // Keep the original subject name
        topic: selectedQuestion.topic, // Keep the original topic name
        topic_name: selectedQuestion.topic_name,
        sub_topic: selectedQuestion.sub_topic, // Keep the original sub-topic
        sub_topic_name: selectedQuestion.sub_topic_name, // Keep the original sub-topic name
        author: selectedQuestion.author,
        status: selectedQuestion.status,
        approval_status: selectedQuestion.approval_status,
        average_score: selectedQuestion.average_score,
        times_attempted: selectedQuestion.times_attempted,
        language: selectedQuestion.language,
        course: selectedQuestion.course,
        subcourse: selectedQuestion.subcourse,
        master_option: selectedQuestion.master_option,
        is_master_option: selectedQuestion.is_master_option,
      };

      if (newImage) {
        console.log("yes new image, passign attachemnt ", newImage);
        payload.attachments = newImage;
      }

      // Dispatch the updateQuestion action
      const response = await dispatch(
        updateQuestion({
          questionSlug: selectedQuestion.slug, // Question ID to update
          data: payload,
        })
      );

      // Check if the response is successful
      if (response.meta && response.meta.requestStatus === "fulfilled") {
        // Close modal and refresh questions
        setShowQuestionModal(false);
        fetchMasterOptions(); // Refetch questions to show updated data
        toast.success("Question updated successfully!");
      } else {
        toast.error("Failed to update the question.");
      }
    } catch (error) {
      console.error("Error updating question", error);
      toast.error("Failed to update the question.");
    }
  };

  // Function to delete a question

  const handleDeleteQuestion = async (slug) => {
    if (!slug) {
      toast.error("Slug is missing, cannot delete the item.");
      return;
    }

    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        const response = await dispatch(deleteQuestion(slug)); // Capture response

        // Check if deletion was successful
        if (response.meta && response.meta.requestStatus === "fulfilled") {
          fetchMasterOptions(); // Refetch questions after deleting
          Swal.fire("Deleted!", "The question has been deleted.", "success");
        } else {
          throw new Error("Delete request failed");
        }
      } catch (error) {
        console.error("Error deleting the question", error);
        toast.error("Failed to delete the question. Please try again.");
      }
    }
  };

  // Function to handle editing an option
  const handleEditOption = (option, questionId) => {
    setSelectedOption(option); // Set the selected option
    setSelectedQuestionId(questionId); // Set the selected question ID
    setUpdatedOptionData({
      option_text: option.option_text,
      is_correct: option.is_correct,
    });
    setShowOptionModal(true); // Open the option edit modal
  };

  // Function to handle deleting an option
  const handleDeleteOption = (optionSlug, questionSlug) => {
    // console.log("Option slug:", optionSlug);
    // console.log("Question slug:", questionSlug);

    Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await dispatch(
            deleteOption({ questionSlug, optionSlug })
          );

          // Check if deletion was successful
          if (response.meta && response.meta.requestStatus === "fulfilled") {
            toast.success("Option deleted successfully!");
            fetchMasterOptions(); // Refetch questions after deletion
          } else {
            throw new Error("Delete request failed");
          }
        } catch (error) {
          console.error("Error deleting option", error);
          toast.error("Failed to delete the option.");
        }
      }
    });
  };

  // Function to handle submitting the updated option
  const handleSubmitOption = async (e) => {
    e.preventDefault();

    try {
      const response = await dispatch(
        updateOption({
          questionSlug: selectedQuestionId,
          optionSlug: selectedOption.slug, // Pass the option ID to update
          data: {
            option_text: updatedOptionData.option_text, // Updated option text
            is_correct: updatedOptionData.is_correct, // Updated correctness
          },
        })
      );

      // Check if update was successful
      if (response.meta && response.meta.requestStatus === "fulfilled") {
        setShowOptionModal(false); // Close the modal after submission
        fetchMasterOptions(); // Refetch questions to show updated options
        toast.success("Option updated successfully!");
      } else {
        throw new Error("Update request failed");
      }
    } catch (error) {
      console.error("Error updating option", error);
      toast.error("Failed to update the option.");
    }
  };

  return (
    <Container className="mt-1">
      <h2
        className="text-center text-success mb-3"
        style={{ fontSize: "1.5rem" }}
      >
        Master Options, <small className="h6"> See live updated here. </small>
      </h2>

      <div className="d-flex justify-content-center mb-3">
        <Form.Control
          type="text"
          placeholder="Search by title"
          value={searchTerm}
          onChange={(e) => onSearchTermChange(e.target.value)}
          style={{ width: "60%" }}
        />
        <DropdownButton
          id="dropdown-basic-button"
          title={`Options per page: ${itemsPerPage}`}
          variant="success"
          onSelect={handleDropdownChange}
        >
          {[2, 5, 10, 25, 50, 100].map((number) => (
            <Dropdown.Item key={number} eventKey={number}>
              {number}
            </Dropdown.Item>
          ))}
        </DropdownButton>
      </div>

      {/* Navigation Buttons */}
      <ButtonGroup className="mb-3">
        <Button
          variant={filter === "all" ? "primary" : "outline-primary"}
          onClick={() => handleFilterClick("all")}
        >
          All
        </Button>
        <Button
          variant={filter === "pending" ? "primary" : "outline-primary"}
          onClick={() => handleFilterClick("pending")}
        >
          Pending
        </Button>
        <Button
          variant={filter === "approved" ? "primary" : "outline-primary"}
          onClick={() => handleFilterClick("approved")}
        >
          Approved
        </Button>
        <Button
          variant={filter === "rejected" ? "primary" : "outline-primary"}
          onClick={() => handleFilterClick("rejected")}
        >
          Rejected
        </Button>
      </ButtonGroup>

      {isLoading ? (
        <QuestionListSkeleton number={3} />
      ) : (
        <div className="d-flex flex-wrap">
          {paginatedOptions.map((option) => (
            <MasterOptionCard
              key={option.slug}
              option={option}
              handleEdit={handleEdit}
              handleDelete={handleDelete}
              handleEditQuestion={handleEditQuestion}
              handleDeleteQuestion={handleDeleteQuestion}
              handleEditOption={handleEditOption}
              handleDeleteOption={handleDeleteOption}
            />
          ))}
          {!paginatedOptions.length && (
            <p className="text-danger mt-3">No questions found.</p>
          )}
        </div>
      )}

      <div className="d-flex justify-content-center mt-1 mb-3">
        <PaginationComponent
          totalPages={totalPages}
          currentPage={currentPage}
          handlePageChange={handlePageChange}
        />
      </div>

      {/* Modal for Editing Question*/}
      {showQuestionModal && (
        <QuestionEditModal
          show={showQuestionModal}
          onHide={() => setShowQuestionModal(false)}
          updatedQuestionData={updatedQuestionData}
          handleSubmitQuestion={handleSubmitQuestion}
          handleQuestionInputChange={handleQuestionInputChange}
          handleImageChange={handleImageChange}
          imagePreview={imagePreview}
          imageSizeText={imageSizeText}
          imageError={imageError}
        />
      )}

      {/* Modal for Editing Option */}
      {showOptionModal && (
        <OptionEditModal
          show={showOptionModal}
          handleClose={() => setShowOptionModal(false)}
          updatedOptionData={updatedOptionData}
          setUpdatedOptionData={setUpdatedOptionData}
          handleSubmitOption={handleSubmitOption}
        />
      )}

      {/* modal for editing master option title and condition  */}
      <MasterOptionPassageModal
        show={showModal}
        handleClose={() => setShowModal(false)}
        updatedData={updatedData}
        handleInputChange={handleInputChange}
        handleSubmit={handleSubmit}
      />
    </Container>
  );
}