import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

// Helper function to get auth token
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor;
  return accessToken;
};

// Base API URL for popups
const API_URL = `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_POPUP_ENDPOINT}`;

const initialState = {
  isLoading: false,
  error: null,
};

export const fetchPopups = createAsyncThunk(
  "popups/fetchAll",
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(API_URL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error fetching popups");
    }
  }
);

export const createPopup = createAsyncThunk(
  "popups/create",
  async (popupData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(API_URL, popupData, {
        headers: { 
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error creating popup");
    }
  }
);

export const updatePopup = createAsyncThunk(
  "popups/update",
  async ({ id, popupData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.patch(`${API_URL}${id}/`, popupData, {
        headers: { 
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error updating popup");
    }
  }
);

export const deletePopup = createAsyncThunk(
  "popups/delete",
  async (id, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(`${API_URL}${id}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return { id };
    } catch (error) {
      return rejectWithValue(error.response?.data || "Error deleting popup");
    }
  }
);

const popupSlice = createSlice({
  name: "popups",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPopups.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPopups.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(fetchPopups.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(createPopup.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createPopup.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(createPopup.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(updatePopup.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updatePopup.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(updatePopup.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(deletePopup.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deletePopup.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(deletePopup.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError } = popupSlice.actions;
export default popupSlice.reducer;
