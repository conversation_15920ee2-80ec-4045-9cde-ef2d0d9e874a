import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor; // Adjust this path to match your state structure
  return accessToken;
};

export const createQuestion = createAsyncThunk(
  "questions/create",
  async ({ data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      let requestBody;
      let headers = {
        Authorization: `Bearer ${token}`,
      };

      // Check if attachments exist
      if (data.attachments instanceof File) {
        const formData = new FormData();

        Object.keys(data).forEach((key) => {
          if (key === "attachments") {
            formData.append("attachments", data.attachments); // File upload
          } else if (Array.isArray(data[key])) {
            // Properly handle array fields in FormData
            data[key].forEach((value) => {
              formData.append(`${key}`, value);
            });
          } else {
            formData.append(key, data[key]);
          }
        });

        requestBody = formData;
        headers["Content-Type"] = "multipart/form-data";
        console.log("Sending FormData:", [...formData.entries()]); // Debugging
      } else {
        requestBody = data; // Send JSON if no file is present
        headers["Content-Type"] = "application/json";
        console.log("Sending JSON:", data); // Debugging
      }

      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_NORMAL_QUESTION}`,
        requestBody,
        { headers }
      );

      return response.data;
    } catch (error) {
      console.error("Error creating question:", error);
      return rejectWithValue(error.response?.data || "Failed to create question");
    }
  }
);


// Get all questions
export const getAllQuestions = createAsyncThunk(
  'questions/getAll',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_NORMAL_QUESTION}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch questions');
    }
  }
);

// Get a specific question
export const getQuestion = createAsyncThunk(
  'questions/get',
  async ({ questionSlug }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_NORMAL_QUESTION}${questionSlug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch question');
    }
  }
);

// Update a question
export const updateQuestion = createAsyncThunk(
  "questions/update",
  async ({ questionSlug, data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      let requestBody;
      let headers = {
        Authorization: `Bearer ${token}`,
      };

      // Check if attachments exist and handle multiple files
      if (data.attachments instanceof File || Array.isArray(data.attachments)) {
        const formData = new FormData();

        Object.keys(data).forEach((key) => {
          if (key === "attachments") {
            if (Array.isArray(data.attachments)) {
              data.attachments.forEach((file) => {
                formData.append("attachments", file);
              });
            } else {
              formData.append("attachments", data.attachments);
            }
          } else if (Array.isArray(data[key])) {
            // Properly handle array fields in FormData
            data[key].forEach((value) => {
              formData.append(`${key}`, value); // Adjust if needed for backend expectations
            });
          } else {
            formData.append(key, data[key]);
          }
        });

        requestBody = formData;
        headers["Content-Type"] = "multipart/form-data";
        console.log("Sending FormData:", [...formData.entries()]); // Debugging
      } else {
        requestBody = data; // Send JSON if no file is present
        headers["Content-Type"] = "application/json";
        console.log("Sending JSON:", data); // Debugging
      }

      const response = await axios.patch(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_NORMAL_QUESTION}${questionSlug}/`,
        requestBody,
        { headers }
      );

      return response.data;
    } catch (error) {
      console.error("Error updating question:", error);
      return rejectWithValue(error.response?.data || "Failed to update question");
    }
  }
);

// Delete a question
export const deleteQuestion = createAsyncThunk(
  'questions/delete',
  async ( questionSlug , { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
         `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_NORMAL_QUESTION}${questionSlug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return questionSlug; // Return the questionSlug to remove the question from the store
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to delete question');
    }
  }
);

const questionSlice = createSlice({
  name: 'questions',
  initialState: { searchTerm: '', loading: false, error: null },
  reducers: {
    setSearchTerm: (state, action) => {
      state.searchTerm = action.payload; // Set search term from input
    },
  },
  extraReducers: (builder) => {
    builder
      // Create question
      .addCase(createQuestion.pending, (state) => {
        state.loading = true;
      })
      .addCase(createQuestion.fulfilled, (state, action) => {
        state.loading = false;
        // Do not store the created question in Redux state
      })
      .addCase(createQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get all questions
      .addCase(getAllQuestions.pending, (state) => {
        state.loading = true;
      })
      .addCase(getAllQuestions.fulfilled, (state, action) => {
        state.loading = false;
        // Do not store the list of questions in Redux state
      })
      .addCase(getAllQuestions.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get a specific question
      .addCase(getQuestion.pending, (state) => {
        state.loading = true;
      })
      .addCase(getQuestion.fulfilled, (state, action) => {
        state.loading = false;
        // Do not store the specific question in Redux state
      })
      .addCase(getQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Update a question
      .addCase(updateQuestion.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateQuestion.fulfilled, (state, action) => {
        state.loading = false;
        // Do not store the updated question in Redux state
      })
      .addCase(updateQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Delete a question
      .addCase(deleteQuestion.pending, (state) => {
        state.loading = true;
      })
      .addCase(deleteQuestion.fulfilled, (state, action) => {
        state.loading = false;
        // Do not store the deleted question in Redux state
      })
      .addCase(deleteQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default questionSlice.reducer;
