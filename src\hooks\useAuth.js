import { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { clearContributorState } from '../Redux/slice/contributorSlice';
import { checkTokenExpiration } from '../utils/axiosInterceptor';

/**
 * Custom hook for authentication management
 * @param {Object} options - Configuration options
 * @param {boolean} options.requireAuth - Whether authentication is required (default: true)
 * @param {string} options.redirectTo - Where to redirect if not authenticated (default: '/contributor_login')
 * @param {boolean} options.showToast - Whether to show toast message on redirect (default: true)
 * @returns {Object} Authentication state and utilities
 */
export const useAuth = (options = {}) => {
  const {
    requireAuth = true,
    redirectTo = '/contributor_login',
    showToast = true
  } = options;

  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const { 
    accessToken, 
    profile, 
    role, 
    loading 
  } = useSelector((state) => state.contributor);

  const isAuthenticated = !!accessToken;

  useEffect(() => {
    if (requireAuth && !loading) {
      if (!accessToken) {
        // No token found
        if (showToast) {
          toast.error('Please log in as a contributor!');
        }
        
        const timer = setTimeout(() => {
          navigate(redirectTo);
        }, 1500);
        
        return () => clearTimeout(timer);
      } else {
        // Token exists, check if it's expired (for JWT tokens)
        const isTokenValid = checkTokenExpiration();
        if (!isTokenValid) {
          // Token is expired, will be handled by the interceptor
          return;
        }
      }
    }
  }, [accessToken, loading, requireAuth, navigate, redirectTo, showToast]);

  // Function to manually logout
  const logout = () => {
    dispatch(clearContributorState());
    if (showToast) {
      toast.success('Logged out successfully');
    }
    navigate(redirectTo);
  };

  // Function to check if user has specific role
  const hasRole = (requiredRole) => {
    return role === requiredRole;
  };

  // Function to check if user has any of the specified roles
  const hasAnyRole = (roles) => {
    return roles.includes(role);
  };

  return {
    isAuthenticated,
    accessToken,
    profile,
    role,
    loading,
    logout,
    hasRole,
    hasAnyRole
  };
};

/**
 * Higher-Order Component for protecting routes
 * @param {React.Component} WrappedComponent - Component to protect
 * @param {Object} options - Configuration options (same as useAuth)
 * @returns {React.Component} Protected component
 */
export const withAuth = (WrappedComponent, options = {}) => {
  return function AuthenticatedComponent(props) {
    const auth = useAuth(options);
    
    // Show loading state while checking authentication
    if (auth.loading) {
      return (
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh' 
        }}>
          <img src="/logoB.png" alt="Loading..." style={{ width: 'auto', height: '3rem' }} />
          <h6 className="text-center text-muted" style={{ marginTop: '1rem' }}>
            Checking authentication...
          </h6>
        </div>
      );
    }

    // Don't render the component if authentication is required but user is not authenticated
    if (options.requireAuth !== false && !auth.isAuthenticated) {
      return null;
    }

    // Render the wrapped component with auth props
    return <WrappedComponent {...props} auth={auth} />;
  };
};

/**
 * Hook specifically for contributor authentication with default settings
 * @returns {Object} Authentication state and utilities
 */
export const useContributorAuth = () => {
  return useAuth({
    requireAuth: true,
    redirectTo: '/contributor_login',
    showToast: true
  });
};

export default useAuth;
