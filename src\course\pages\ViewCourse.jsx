import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams, Link } from "react-router-dom";
import {
  Row,
  Col,
  Card,
  Button,
  Container,
  Form,
  Modal,
  Pagination,
  Dropdown,
  DropdownButton,
} from "react-bootstrap";
import { getCourse } from "../../redux/slice/courseSlice";
import {
  updateSubCourse,
  deleteSubCourse,
} from "../../redux/slice/subCourseSlice";
import AddSubCourses from "../components/AddSubCourses";
import NavigationBar from "../../commonComponents/NavigationBar";
import ViewModal from "../../commonComponents/ViewModal";

import { BsPencilSquare, BsTrash } from "react-icons/bs";
import toast, { Toaster } from "react-hot-toast";
import Swal from "sweetalert2";
import CourseCardsSkeleton from "../../commonComponents/CourseCardsSkeleton";
import Skeleton from "react-loading-skeleton";
import PaginationComponent from "../../commonComponents/PaginationComponent";

const ViewCourse = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { courseSlug } = useParams();

  const accessToken = useSelector(
    (state) => state.contributor.accessToken || null
  );

  const [searchQuery, setSearchQuery] = useState("");
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedSubCourse, setSelectedSubCourse] = useState(null);
  const [subCourseName, setSubCourseName] = useState("");
  const [subCourseDescription, setSubCourseDescription] = useState("");
  const [courseValue, setCourseValue] = useState("");
  const [editModalOpen, setEditModalOpen] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [subCoursesPerPage, setSubCoursesPerPage] = useState(5);

  const [course, setCourse] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!accessToken) {
      toast.error("Please log in as a contributor!");
      const timer = setTimeout(() => {
        navigate("/contributor_login");
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [accessToken, navigate]);

  const fetchCourseDetails = async () => {
    setIsLoading(true);
    try {
      if (courseSlug) {
        const courseResponse = await dispatch(getCourse(courseSlug));
        if (courseResponse?.payload) {
          setCourse(courseResponse.payload);
        }
      }
    } catch (error) {
      console.error("Error fetching course details:", error);
      toast.error("Failed to load course details.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCourseDetails();
  }, [dispatch, courseSlug]);

  // for view modal

  const handleViewSubCourse = (subCourse) => {
    setSelectedSubCourse(subCourse);
    setShowViewModal(true);
  };

  const subCourses = course?.sub_courses || [];

  const filteredSubCourses = subCourses.filter(
    (subCourse) =>
      (subCourse.name &&
        subCourse.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (subCourse.description &&
        subCourse.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const indexOfLastSubCourse = currentPage * subCoursesPerPage;
  const indexOfFirstSubCourse = indexOfLastSubCourse - subCoursesPerPage;
  const currentSubCourses = filteredSubCourses.slice(
    indexOfFirstSubCourse,
    indexOfLastSubCourse
  );

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleItemsPerPageChange = (itemsPerPage) => {
    setSubCoursesPerPage(itemsPerPage);
    setCurrentPage(1);
  };

  const handleDeleteSubCourse = async (slug) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deleteSubCourse(slug));
        fetchCourseDetails();
        toast.success("Sub-course deleted successfully!");
      } catch (error) {
        console.error("Error deleting sub-course:", error);
        toast.error("Failed to delete the sub-course. Please try again.");
      }
    }
  };

  const handleEditSubCourse = (subCourse) => {
    setSelectedSubCourse(subCourse);
    setSubCourseName(subCourse.name);
    setSubCourseDescription(subCourse.description);
    setCourseValue(subCourse.course);
    setEditModalOpen(true);
  };

  const handleSaveSubCourse = async () => {
    if (selectedSubCourse) {
      try {
        await dispatch(
          updateSubCourse({
            slug: selectedSubCourse.slug,
            updatedData: {
              name: subCourseName,
              description: subCourseDescription,
              course: courseValue,
            },
          })
        );
        setEditModalOpen(false);
        setSelectedSubCourse(null);
        setSubCourseName("");
        setSubCourseDescription("");
        setCourseValue("");
        fetchCourseDetails();
      } catch (error) {
        console.error("Error updating sub-course:", error);
        toast.error("Failed to update sub-course.");
      }
    }
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleViewModalOpen = (subCourse) => {
    setSelectedSubCourse(subCourse);
    setViewModalOpen(true);
  };

  const handleCloseViewModal = () => {
    setViewModalOpen(false);
    setSelectedSubCourse(null);
  };

  // if (isLoading) {
  //   return (
  //     // <CourseCardsSkeleton number={6}/>
  //   );
  // }

  return (
    <>
      <NavigationBar />
      <Container>
        <Row>
          <Col xs={12} md={4} lg={3}>
            <AddSubCourses
              slug={courseSlug}
              onSubCourseAdded={fetchCourseDetails}
            />
          </Col>
          <Col xs={12} md={8} lg={9}>
            <div className="view-course-container mt-5">
              <h2 className="text-center text-success mb-4 h4">
                View Course / Sub-Course
              </h2>
              <Row className="mb-1 justify-content-center">
                <Col xs={12} sm={8} md={6} lg={6}>
                  <Card className="shadow-lg rounded-3 mb-4">
                    <Card.Body>
                      {isLoading ? (
                        <>
                          <Skeleton
                            height={20}
                            width="60%"
                            baseColor="#e6ffe6"
                            highlightColor="#c4f7c4"
                          />
                          <Skeleton
                            height={15}
                            width="90%"
                            baseColor="#e6ffe6"
                            highlightColor="#c4f7c4"
                            className="mt-2"
                          />
                        </>
                      ) : (
                        <>
                          <Card.Title className="text-success">
                            {course?.name}
                          </Card.Title>
                          <Card.Text>{course?.description}</Card.Text>
                        </>
                      )}
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
              <Row className="mb-4 justify-content-center">
                <Col xs={12} sm={8} md={6} lg={5} className="d-flex align-items-center">
                  <Form.Control
                    type="text"
                    placeholder="Search sub-courses..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                    className="me-2"
                  />
                  <DropdownButton
                    id="dropdown-items-per-page"
                    title={`${subCoursesPerPage} per page`}
                    variant="success"
                    onSelect={(e) => handleItemsPerPageChange(Number(e))}
                  >
                    <Dropdown.Item eventKey="5">5 per page</Dropdown.Item>
                    <Dropdown.Item eventKey="25">25 per page</Dropdown.Item>
                    <Dropdown.Item eventKey="50">50 per page</Dropdown.Item>
                    <Dropdown.Item eventKey="100">100 per page</Dropdown.Item>
                    <Dropdown.Item eventKey={filteredSubCourses.length}>All</Dropdown.Item>
                  </DropdownButton>
                </Col>
              </Row>
              {isLoading ? (
                <CourseCardsSkeleton number={6} />
              ) : (
                <Row>
                  {currentSubCourses.length > 0 ? (
                    currentSubCourses.map((subCourse) => (
                      <Col key={subCourse.slug} xs={12} sm={6} md={6} lg={4}>
                        <Card className="mb-4 shadow-sm rounded-3">
                          <Card.Body>
                            <Card.Title className="text-success text-truncate w-100">
                              {subCourse.name}
                            </Card.Title>
                            <Card.Text className="text-truncate w-100">
                              {subCourse.description}
                            </Card.Text>
                            <div className="d-flex flex-wrap justify-content-center">
                              <Link to={`/add_tier/${subCourse.slug}`}>
                                <Button
                                  variant="outline-primary"
                                  className="m-1 fs-6 "
                                >
                                  Tier
                                </Button>
                              </Link>
                              <Button
                                variant="outline-info"
                                className="m-1 fs-6 "
                                onClick={() => handleViewSubCourse(subCourse)}
                              >
                                View
                              </Button>

                              <Button
                                variant="outline-success"
                                className="m-1 fs-6 "
                                onClick={() => handleEditSubCourse(subCourse)}
                              >
                                <BsPencilSquare />
                              </Button>
                              <Button
                                variant="outline-danger"
                                className="m-1 fs-6 "
                                onClick={() =>
                                  handleDeleteSubCourse(subCourse.slug)
                                }
                              >
                                <BsTrash />
                              </Button>
                            </div>
                          </Card.Body>
                        </Card>
                      </Col>
                    ))
                  ) : (
                    <div className="col-12 text-center">
                      No sub-courses found
                    </div>
                  )}
                </Row>
              )}

              <div className="d-flex justify-content-center mt-4">
                <PaginationComponent
                  totalPages={Math.ceil(filteredSubCourses.length / subCoursesPerPage)}
                  currentPage={currentPage}
                  handlePageChange={handlePageChange}
                />
              </div>
            </div>
          </Col>
        </Row>
        <Toaster />
      </Container>

      {/* Edit Modal */}
      <Modal show={editModalOpen} onHide={() => setEditModalOpen(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Edit Sub-Course</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group controlId="subCourseName">
              <Form.Label>Sub-Course Name</Form.Label>
              <Form.Control
                type="text"
                value={subCourseName}
                onChange={(e) => setSubCourseName(e.target.value)}
              />
            </Form.Group>
            <Form.Group controlId="subCourseDescription" className="mt-3">
              <Form.Label>Sub-Course Description</Form.Label>
              <Form.Control
                type="text"
                value={subCourseDescription}
                onChange={(e) => setSubCourseDescription(e.target.value)}
              />
            </Form.Group>
            <Form.Group controlId="courseValue" className="mt-3 d-none">
              <Form.Label>Course</Form.Label>
              <Form.Control
                type="text"
                value={courseValue}
                onChange={(e) => setCourseValue(e.target.value)}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setEditModalOpen(false)}>
            Close
          </Button>
          <Button variant="primary" onClick={handleSaveSubCourse}>
            Save Changes
          </Button>
        </Modal.Footer>
      </Modal>

      <ViewModal
        show={showViewModal}
        onHide={() => setShowViewModal(false)}
        content={selectedSubCourse}
      />
    </>
  );
};

export default ViewCourse;
