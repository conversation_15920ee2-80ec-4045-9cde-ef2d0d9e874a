import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { HelmetProvider } from 'react-helmet-async';
import App from './App.jsx';
import { store } from './redux/store.js';
import { persistor } from './redux/persistor.js';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css'; // Make sure to import the default styles
import './index.css';

// Math CSS is loaded via CDN in index.html for better compatibility

// Constants for persistence cleanup
const PERSIST_KEY = 'root';
const HOUR_IN_MS = 60 * 60 * 1000;
const EXPIRE_TIME_IN_MS = 6 * HOUR_IN_MS; // 6 hours in milliseconds

// Function to check if persisted state has expired
const checkPersistExpiration = () => {
  try {
    const savedTimestamp = localStorage.getItem(`${PERSIST_KEY}_timestamp`);

    if (savedTimestamp) {
      const currentTime = Date.now();
      const elapsedTime = currentTime - parseInt(savedTimestamp, 10);

      if (elapsedTime > EXPIRE_TIME_IN_MS) {
        // Clear persisted state
        persistor.purge();
        localStorage.removeItem(`${PERSIST_KEY}_timestamp`);
        console.log('Persisted state cleared after 6 hours.');
      }
    } else {
      // Set the current timestamp if not already set
      localStorage.setItem(`${PERSIST_KEY}_timestamp`, Date.now());
    }
  } catch (error) {
    console.error('Error accessing localStorage:', error);
  }
};

// Check expiration on app load
checkPersistExpiration();

// Set an interval to check every hour if the app remains open
setInterval(checkPersistExpiration, HOUR_IN_MS); // Every 1 hour

// Service Worker Registration
if ('serviceWorker' in navigator) {
  navigator.serviceWorker
    .register('/firebase-messaging-sw.js', { scope: '/' })
    .then((registration) => {
      console.log('Service Worker registered with scope:', registration.scope);
    })
    .catch((error) => {
      console.error('Service Worker registration failed:', error);
    });
}

// Render the App
createRoot(document.getElementById('root')).render(
  <StrictMode>
    <Provider store={store}>
      <PersistGate
        loading={
          <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
            <img src="/logoB.png" alt="Loading..." style={{ width: 'auto', height: '3rem' }} />
            <h6 className="text-center text-muted" style={{ marginLeft: '1rem' }}>Loading......</h6>
          </div>
        }
        persistor={persistor}
      >
        <HelmetProvider>
          <App />
        </HelmetProvider>
      </PersistGate>
    </Provider>
  </StrictMode>
);
