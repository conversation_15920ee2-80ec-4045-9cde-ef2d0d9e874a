import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Initial state structure
const initialState = {
  topics: [],
  topic: [],
  allTopics: [],
  loading: false,
  error: null,
};

// Base URL from environment variables
const baseUrl = import.meta.env.VITE_BASE_URL;

// Create Topic Thunk
export const createTopic = createAsyncThunk(
  'topic/createTopic',
  async ({ topicData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${baseUrl}${import.meta.env.VITE_CREATE_TOPIC}`,
        topicData
      );
      if (response.status === 200) {
        return response.data; // Return the topic object directly
      }
      throw new Error(response.data.message || 'Error creating topic');
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error creating topic');
    }
  }
);

// Get Topics Thunk
export const getTopics = createAsyncThunk(
  'topic/getTopics',
  async (_, { rejectWithValue }) => {
    try { 
      const response = await axios.get(`${baseUrl}${import.meta.env.VITE_GET_TOPICS}`);
      if (response.status === 200) {
        return response.data; // Return the topic object directly
      }
      throw new Error(response.data.message || 'Error fetching topics');
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching topics');
    }
  }
);

// Get Single Topic Thunk
export const getSingleTopic = createAsyncThunk(
  'topic/getSingleTopic',
  async (topic_slug, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${baseUrl}${import.meta.env.VITE_GET_TOPIC}${topic_slug}/`);
      
      // Check if the response status is 200
      if (response.status === 200) {
        return response.data; // Return the topic object directly
      }
      
      throw new Error(response.data.message || 'Error fetching single topic');
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching single topic');
    }
  }
);


// Update Topic Thunk
export const updateTopic = createAsyncThunk(
  'topic/updateTopic',
  async ({ topicSlug, updatedData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(
        `${baseUrl}${import.meta.env.VITE_UPDATE_TOPIC}${topicSlug}/`,
        updatedData
      );
      if (response.status === 200) {
        return response.data; // Return the topic object directly
      }
      throw new Error(response.data.message || 'Error updating topic');
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error updating topic');
    }
  }
);

// Delete Topic Thunk
export const deleteTopic = createAsyncThunk(
  'topic/deleteTopic',
  async ({ topicSlug }, { rejectWithValue }) => {
    try {
      const response = await axios.delete(
        `${baseUrl}${import.meta.env.VITE_DELETE_TOPIC}${topicSlug}/`
      );
    
      if (response.status === 200) {
        return topicSlug; // Return the ID of the deleted topic
      }
      throw new Error(response.data.message || 'Error deleting topic');
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error deleting topic');
    }
  }
);

// Topic Slice
const topicSlice = createSlice({
  name: 'topic',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(createTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createTopic.fulfilled, (state, action) => {
        state.allTopics.push(action.payload);
        state.loading = false;
      })
      .addCase(createTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      .addCase(getTopics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getTopics.fulfilled, (state, action) => {
        state.allTopics = action.payload;
        state.loading = false;
      })
      .addCase(getTopics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      .addCase(getSingleTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSingleTopic.fulfilled, (state, action) => {
        state.topic = action.payload;
        state.loading = false;
      })
      .addCase(getSingleTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      .addCase(updateTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateTopic.fulfilled, (state, action) => {
        const index = state.allTopics.findIndex((topic) => topic.id === action.payload.id);
        if (index !== -1) {
          state.allTopics[index] = action.payload;
        }
        state.loading = false;
      })
      .addCase(updateTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      .addCase(deleteTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteTopic.fulfilled, (state, action) => {
        state.allTopics = state.allTopics.filter((topic) => topic.id !== action.payload);
        state.loading = false;
      })
      .addCase(deleteTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default topicSlice.reducer;
