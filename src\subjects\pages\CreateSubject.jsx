import React, { useState } from "react";
import { But<PERSON>, Form, Container, Row, Col } from "react-bootstrap";
import { useDispatch } from "react-redux";
import { createSubject } from "../../redux/slice/subjectSlice"; // Redux action to add a new subject
import { Link } from "react-router-dom";
import AllSubjects from "../components/AllSubjects"; // Import the component to show subjects
import NavigationBar from "../../commonComponents/NavigationBar"; // NavigationBar

const CreateSubject = () => {
  const dispatch = useDispatch();

  // States for subject form
  const [subjectName, setSubjectName] = useState("");
  const [subjectDescription, setSubjectDescription] = useState("");
  const [subjectAdded, setSubjectAdded] = useState(false); // Flag to track if subject was added

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    const newSubject = {
      subject_id: Date.now(), // Unique ID for the subject
      name: subjectName,
      description: subjectDescription,
    };

    // Dispatch the action to add the subject
    dispatch(createSubject(newSubject));

    // Reset form inputs
    setSubjectName("");
    setSubjectDescription("");

    // Set subjectAdded flag to true to trigger refresh in AllSubjects
    setSubjectAdded(true);

    setTimeout(() => {
      setSubjectAdded(false); // Reset the flag to false
      // You can dispatch an action to fetch updated subjects if needed
    });
  };

  return (
    <>
      <NavigationBar />
      <Container style={{ height: "90vh", paddingTop: "20px" }}>
        <Row style={{ height: "100%" }}>
          {/* Left Section: Add Subject Form */}
          <Col xs={12} md={3} lg={3}>
            <Form onSubmit={handleSubmit} className="border mt-5 p-4 rounded shadow-lg">
              <Form.Group controlId="subjectName" className="mb-3">
                <Form.Label>Subject Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter subject name"
                  value={subjectName}
                  onChange={(e) => setSubjectName(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="subjectDescription" className="mb-3">
                <Form.Label>Subject Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={4}
                  placeholder="Enter subject description"
                  value={subjectDescription}
                  onChange={(e) => setSubjectDescription(e.target.value)}
                  required
                />
              </Form.Group>

              <Button variant="success" type="submit" className="w-100">
                Add Subject
              </Button>
            </Form>

            <div className="d-flex justify-content-center align-items-center">
              <Link to="/contributor_dashboard">
                <Button variant="secondary" className="mt-5 text-center">
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </Col>

          {/* Right Section: Live Subjects Dashboard */}
          <Col xs={12} md={9} lg={9} className="border-left">
            <AllSubjects subjectAdded={subjectAdded} /> {/* Pass the flag to trigger re-fetch */}
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default CreateSubject;
