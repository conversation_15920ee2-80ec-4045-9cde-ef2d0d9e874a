import React, { useEffect, useState } from "react";
import { Card, Container, Row, Col, Button } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { getAllBlogs } from "../../redux/slice/blogSlice";
import toast from "react-hot-toast";
import { useDispatch } from "react-redux";
import LoadingScreen from "../../commonComponents/LoadingScreen";
import NavigationBar from "../../commonComponents/NavigationBar";
import { FaEye } from "react-icons/fa";
import Skeleton from "react-loading-skeleton"; // Import skeleton component

const Blogs = () => {
  const [blogs, setBlogs] = useState();
  const [isLoading, setIsLoading] = useState(true); // State for loading
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const baseUrl = import.meta.env.VITE_BASE_URL;
  const defaultImage = "../../../../assets/deafult.jpeg";

  const handleButtonClick = () => {
    navigate("/shorts");
  };

  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://connect.facebook.net/en_GB/sdk.js#xfbml=1&version=v21.8";
    script.async = true;
    script.defer = true;
    script.onload = () => {
      window.FB.XFBML.parse(); // Force Facebook to render the button once the script is loaded
    };
    document.body.appendChild(script);
    
    return () => {
      // Cleanup script tag when component unmounts
      document.body.removeChild(script);
    };
  }, []);

  useEffect(() => {
    // Check if the screen width is <= 767px on mount
    if (window.innerWidth <= 450) {
      navigate("/shorts");
    }

    // Fetch blogs when the component mounts
    const fetchData = async () => {
      setIsLoading(true); // Set loading to true
      try {
        const response = await dispatch(getAllBlogs()); // Dispatch getAllBlogs action to get the data
        if (response?.payload) {
          setBlogs(response.payload); // Set the blogs in the state
        } else {
          toast.error("Error fetching blogs");
        }
      } catch (error) {
        toast.error("Error fetching blogs");
      } finally {
        setIsLoading(false); // Set loading to false after fetching is done
      }
    };

    fetchData();

    // Listen to window resize events and check for screen width
    const handleResize = () => {
      if (window.innerWidth <= 767) {
        navigate("/shorts");
      }
    };

    window.addEventListener("resize", handleResize);

    // Cleanup the event listener on component unmount
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [dispatch, navigate]);

  // Show only the loading screen if data is loading
  // if (isLoading) {
  //   return <LoadingScreen />;
  // }

  return (
    <>
      <NavigationBar />
      <Container>
        <Row className="justify-content-center">
          <Col xs={6} md={12}>
            {/* <h2 className="text-center text-success my-4" style={{ fontSize: '1.5rem' }}>
            Blogs
          </h2> */}
          </Col>
          <Col xs={6} className="d-md-none mb-4">
            <Button
              variant="primary"
              style={{ width: "100%" }}
              onClick={handleButtonClick}
            >
              Shorts Mode
            </Button>
          </Col>
        </Row>

        <Row className="mt-5">
          {isLoading ? (
            // Display skeletons while loading
            Array(6)
              .fill()
              .map((_, index) => (
                <Col xs={12} sm={6} md={4} lg={4} key={index} className="mb-4 border border-0">
                  <Card className="mb-4 rounded">
                    <Skeleton height={200} />
                    <Card.Body className="d-flex flex-column">
                      <Skeleton count={3} />
                    </Card.Body>
                  </Card>
                </Col>
              ))
          ) : (
            // Display actual blogs once loading is done
            blogs &&
            blogs.map((blog) => (
              <Col xs={12} sm={6} md={4} lg={4} key={blog.id} className="mb-4">
                <Card
                  className="mb-4 shadow-sm rounded"
                  style={{ overflow: "hidden", border: "none" }}
                >
                  {/* Card Image */}
                  <div
                    style={{
                      height: "200px",
                      overflow: "hidden",
                      position: "relative",
                    }}
                  >
                    <Card.Img
                      variant="top"
                      src={`${baseUrl}${
                        blog.image ? blog.image : defaultImage
                      }`}
                      alt={blog.image_caption || "Blog Image"}
                      style={{
                        objectFit: "cover",
                        height: "100%",
                        width: "100%",
                        transition: "transform 0.3s ease-in-out",
                      }}
                      className="hover-zoom"
                    />
                  </div>

                  {/* Card Body */}
                  <Card.Body className="d-flex flex-column">
                    <div className="mb-2">
                      <Card.Title className="fw-bold text-black text-truncate">
                        {blog.title}
                      </Card.Title>
                      <Card.Subtitle className="mb-2 text-muted">
                        By{" "}
                        <span className="fw-semibold">
                          {" "}
                          {blog.author_first_name} {blog.author_last_name}{" "}
                        </span>
                      </Card.Subtitle>
                      <Card.Text>
                        <small className="text-muted">
                          Published on:{" "}
                          {new Date(blog.published_date).toLocaleDateString()}
                        </small>
                      </Card.Text>
                    </div>

                    <Card.Text className="flex-grow-1 text-truncate">
                      {blog.meta_description}
                    </Card.Text>

                    {/* View More Button */}
                    <div className="d-flex justify-content-between align-items-center">
                      <div className="d-flex align-items-center">
                        <FaEye
                          style={{ marginRight: "5px", color: "#6c757d" }}
                        />
                        <small className="text-muted">
                          {blog?.visit_count || 0}
                        </small>
                      </div>

                      <div className="text-center mt-3">
                        <Link
                          to={`/blog/${blog.slug}`}
                          style={{ textDecoration: "none" }}
                        >
                          <Row className="mb-3">
                            <Col className="d-flex justify-content-center align-items-center text-success">
                              <p className="fw-bold text-success mb-0" style={{ marginRight: "5px"}}>
                                Read more
                              </p>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                strokeWidth="2.5"
                                stroke="currentColor"
                                style={{
                                  width: "20px",
                                  height: "20px",
                                }}
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3"
                                />
                              </svg>
                            </Col>
                          </Row>
                        </Link>
                      </div>
                    </div>
                    {/* Facebook Like Button */}
                    <div className="d-flex justify-content-center align-items-center mt-3">
                      <div
                        className="fb-like"
                        data-href={`https://blog.shashtrarth.com/blog/${blog.slug}`}
                        data-width=""
                        data-layout="button"
                        data-action="like"
                        data-size="small"
                        data-share="true"
                      ></div>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ))
          )}
        </Row>
      </Container>
    </>
  );
};

export default Blogs;
