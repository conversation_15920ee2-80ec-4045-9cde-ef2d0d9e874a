import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistReducer, persistStore } from 'redux-persist';
import storage from 'redux-persist/lib/storage'; // Use local storage for persistence

// Import reducers
import contributorReducer from './slice/contributorSlice';
import questionReducer from './slice/questionSlice';
import masterQuestionReducer from './slice/masterQuestionSlice';
import masterOptionReducer from './slice/masterOptionSlice';
import courseReducer from './slice/courseSlice';
import paperEngineReducer from './slice/paperEngineSlice';
import optionsReducer from './slice/optionsSlice';
import subjectReducer from './slice/subjectSlice';
import topicReducer from './slice/topicsSlice';
import subTopicReducer from './slice/subTopicSlice';
import previousYearQuestionReducer from './slice/previousYearQuestionSlice';
import blogReducer from './slice/blogSlice';
import popupReducer from './slice/popupSlice';
import sopReducer from './slice/sopSlice';

// Persist config for redux-persist
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['contributor'], // Persist only these slices
};

// Combine reducers
const appReducer = combineReducers({
  contributor: contributorReducer,
  questions: questionReducer,
  masterQuestion: masterQuestionReducer,
  masterOption: masterOptionReducer,
  course: courseReducer,
  paperEngine: paperEngineReducer,
  options: optionsReducer,
  subject: subjectReducer,
  topic: topicReducer,
  subTopic: subTopicReducer,
  previousYearQuestion: previousYearQuestionReducer,
  blogs: blogReducer,
  popups: popupReducer,
  sop: sopReducer,
});

// Root reducer to reset specific slices and non-persisted state
const rootReducer = (state, action) => {
  if (action.type === 'LOGOUT_CONTRIBUTOR') {
    return appReducer(undefined, action); // Clears all state except persisted ones
  }
  return appReducer(state, action);
};

// Create persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);


// Create the Redux store
export const store = configureStore({
  reducer: persistedReducer,
 devTools: import.meta.env.VITE_REDUX_DEVTOOLS === 'true', // Enable DevTools only if the environment variable is true
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false, // Disables serializability checks
    }),
});

// Create persistor
export const persistor = persistStore(store);

export default store;
