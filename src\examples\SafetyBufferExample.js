/**
 * Example demonstrating the 10-minute safety buffer for token validation
 * This is for educational purposes to show how the safety buffer works
 */

import { 
  isTokenValid, 
  getTokenExpiration, 
  getEffectiveExpiration,
  getTimeUntilExpirationString,
  DEFAULT_SAFETY_BUFFER_MINUTES 
} from '../utils/authUtils';

// Example JWT token (this is just for demonstration - don't use in production)
const createExampleToken = (expirationMinutesFromNow) => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    sub: 'user123',
    exp: Math.floor(Date.now() / 1000) + (expirationMinutesFromNow * 60),
    iat: Math.floor(Date.now() / 1000)
  }));
  const signature = 'fake-signature-for-demo';
  
  return `${header}.${payload}.${signature}`;
};

// Example usage
console.log('=== Safety Buffer Example ===\n');

// Create tokens with different expiration times
const tokens = {
  'expires_in_5_minutes': createExampleToken(5),
  'expires_in_15_minutes': createExampleToken(15),
  'expires_in_30_minutes': createExampleToken(30),
  'expired_5_minutes_ago': createExampleToken(-5)
};

Object.entries(tokens).forEach(([description, token]) => {
  console.log(`Token: ${description}`);
  console.log(`  Actual expiration: ${getTokenExpiration(token)?.toLocaleString()}`);
  console.log(`  Effective expiration (with ${DEFAULT_SAFETY_BUFFER_MINUTES}min buffer): ${getEffectiveExpiration(token)?.toLocaleString()}`);
  console.log(`  Is valid (with safety buffer): ${isTokenValid(token)}`);
  console.log(`  Is valid (without safety buffer): ${isTokenValid(token, 0)}`);
  console.log(`  Time until expiration: ${getTimeUntilExpirationString(token)}`);
  console.log('');
});

// Demonstrate different safety buffer values
console.log('=== Different Safety Buffer Values ===\n');

const testToken = createExampleToken(8); // Token expires in 8 minutes

console.log('Token expires in 8 minutes:');
console.log(`  With 5-minute buffer: ${isTokenValid(testToken, 5)} (valid)`);
console.log(`  With 10-minute buffer: ${isTokenValid(testToken, 10)} (invalid - expired)`);
console.log(`  With 15-minute buffer: ${isTokenValid(testToken, 15)} (invalid - expired)`);
console.log('');

// Real-world scenario explanation
console.log('=== Real-World Scenario ===\n');
console.log('Scenario: Server token expires at 3:00 PM, but client clock is 2 minutes fast');
console.log('');
console.log('Without safety buffer:');
console.log('  - Client thinks it\'s 3:02 PM when server thinks it\'s 3:00 PM');
console.log('  - Client makes API call thinking token is expired');
console.log('  - Server rejects call because token is still valid');
console.log('  - User gets confusing error');
console.log('');
console.log('With 10-minute safety buffer:');
console.log('  - Client treats token as expired at 2:50 PM');
console.log('  - Even with 2-minute fast clock, client stops using token at 2:52 PM server time');
console.log('  - Server still considers token valid until 3:00 PM');
console.log('  - No authentication errors occur');
console.log('  - User gets smooth redirect to login');

export { createExampleToken };
