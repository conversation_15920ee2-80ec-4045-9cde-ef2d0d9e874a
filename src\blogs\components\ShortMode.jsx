import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, But<PERSON> } from "react-bootstrap";
import { motion, AnimatePresence } from "framer-motion";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import toast from "react-hot-toast";
import { getAllBlogs } from "../../redux/slice/blogSlice"; // Assuming your getAllBlogs action is defined in this file
import NavigationBar from "../../commonComponents/NavigationBar";

const SwipeableBlogs = () => {
  const [blogs, setBlogs] = useState([]); // State to hold fetched blogs
  const [currentIndex, setCurrentIndex] = useState(0);
  const [visitCount, setVisitCount] = useState(0);
  const [direction, setDirection] = useState(0);
  const [showNoBlogsMessage, setShowNoBlogsMessage] = useState(false);
  const [scrollVisible, setScrollVisible] = useState(true); // Initially set the scroll button to be visible
  const dispatch = useDispatch();
  const navigate = useNavigate(); // Initialize useNavigate hook
  const baseUrl = import.meta.env.VITE_BASE_URL;
  const defaultImage = '../../../../assets/deafult.jpeg';

  // Fetch blogs data from the backend
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await dispatch(getAllBlogs()); // Dispatch getAllBlogs action to get the data
        if (response?.payload) {
          setBlogs(response.payload); // Set the blogs in the state
        } else {
          toast.error("Error fetching blogs");
        }
      } catch (error) {
        toast.error("Error fetching blogs");
      }
    };

    fetchData();
  }, [dispatch]);

  // Redirect to /blogs if window width is more than 451px
  useEffect(() => {
    // Check the screen size when the component mounts
    if (window.innerWidth > 451) {
      navigate('/blogs');
    }

    const handleResize = () => {
      if (window.innerWidth > 451) {
        navigate('/blogs');
      }
    };

    // Set up event listener for window resize
    window.addEventListener("resize", handleResize);

    // Cleanup event listener on component unmount
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [navigate]);


  useEffect(() => {
    // Increment the visit count whenever the current index changes
    setVisitCount((prevCount) => prevCount + 1);
  }, [currentIndex]);

  const getRandomBlogIndex = () => {
    const randomIndex = Math.floor(Math.random() * blogs.length);
    return randomIndex;
  };

  const changeBlog = (swipeDirection) => {
    let newIndex = getRandomBlogIndex();
    while (newIndex === currentIndex) {
      newIndex = getRandomBlogIndex();
    }
    setDirection(swipeDirection);
    setCurrentIndex(newIndex);
  };

  const handleTouchStart = (e) => {
    e.target.startY = e.touches[0].clientY;
  };

  const handleTouchEnd = (e) => {
    const endY = e.changedTouches[0].clientY;
    const deltaY = e.target.startY - endY;

    if (deltaY > 50) changeBlog(1); // Swipe up
    if (deltaY < -50) changeBlog(-1); // Swipe down
  };

  const variants = {
    enter: (direction) => ({
      y: direction > 0 ? "100%" : "-100%", // Coming from top or bottom
      opacity: 0,
    }),
    center: {
      y: 0,
      opacity: 1,
    },
    exit: (direction) => ({
      y: direction > 0 ? "-100%" : "100%", // Exiting to top or bottom
      opacity: 0,
    }),
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setScrollVisible(false); // Hide the scroll button after 4 seconds
    }, 4000); // 4 seconds timeout

    return () => clearTimeout(timer); // Clear the timeout if the component unmounts before 4 seconds
  }, []);

  const scrollToMore = () => {
    window.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior: "smooth",
    });
  };

  return (
    <>
      <NavigationBar />
      <Container
        fluid
        className="mt-5 d-flex justify-content-center vh-120"
        style={{
          overflow: "hidden",
          backgroundColor: "#f8f9fa",
          touchAction: "none",
          padding: 0, // Ensure no padding
        }}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
      >
        <AnimatePresence initial={false} custom={direction}>
          {!showNoBlogsMessage && blogs.length > 0 ? (
            <motion.div
              key={blogs[currentIndex]?.id || "placeholder"}
              custom={direction}
              variants={variants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                y: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              className="position-absolute w-100 h-100 d-flex justify-content-center align-items-center"
            >
              <Card
                className="text-center"
                style={{
                  width: "100%", // Full width
                  height: "100%", // Full height
                  overflow: "hidden", // Prevent overflow
                  border: "none", // No border
                  padding: "10px", // Remove padding to make use of full screen
                }}
              >
                <Card.Img
                  variant="top"
                  src={`${baseUrl}${blogs[currentIndex].image ? blogs[currentIndex].image : defaultImage}`} // Use default image if not available
                  alt={`Blog ${blogs[currentIndex]?.id}`}
                  className="rounded"
                  style={{
                    height: "auto", // Maintain aspect ratio
                    objectFit: "cover", // Make the image cover the container
                    width: "100%",
                  }}
                />
                <Card.Body
                  style={{
                    height: "auto", // Let the content take necessary space
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "flex-start", // Align text at the top
                    textAlign: "left", // Align text to the left for readability
                    padding: "10px", // Padding for content inside
                    overflowY: "auto", // Allow vertical scrolling if content overflows
                  }}
                >
                  <Card.Title
                    style={{
                      fontSize: "1.5rem", // Slightly larger font for the title
                      fontWeight: "bold", // Bold title for emphasis
                      marginBottom: "10px", // Space between title and content
                    }}
                  >
                    {blogs[currentIndex]?.title}
                  </Card.Title>
                  <Card.Text
                    style={{
                      fontSize: "1rem", // Readable text size
                      lineHeight: "1.6", // Increase line height for better readability
                      marginBottom: "10px", // Space between paragraphs
                    }}
                  >
                    {blogs[currentIndex]?.short_content}
                  </Card.Text>
                  <Card.Text
                  style={{
                    fontSize: "0.9rem",
                    color: "gray",
                    fontStyle: "italic",
                    marginTop: "10px",
                  }}
                >
                  Visits: {visitCount} {/* Display the visit count */}
                </Card.Text>

                  {/* Button to navigate to the full article */}
                  <Link to={`/blog/${blogs[currentIndex]?.slug}`}>
                  <Button
                      // variant="primary"
                      className="fancy-button"
                      style={{
                        marginTop: "20px", // Space between content and button
                        width: "100%", // Full width of the container
                      }}
                    >
                      <span>Read Full Article</span>
                    </Button>
                  </Link>
                </Card.Body>
              </Card>
            </motion.div>
          ) : (
            <motion.div
              key="no-blogs"
              initial={{ opacity: 0, y: "50%" }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: "-50%" }}
              transition={{ duration: 0.5 }}
              className="position-absolute w-100 h-100 d-flex justify-content-center align-items-center"
            >
              <h2 style={{ color: "#6c757d" }}>Loading blogs!</h2>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Scroll to view more button */}
        {scrollVisible && (
          <motion.div
            className="scroll-to-more"
            initial={{ opacity: 1, y: 20 }}
            animate={{
              opacity: 1,
              y: [0, -10, 0], // Bounce effect (go up and then back down)
            }}
            exit={{ opacity: 1, y: 20 }}
            transition={{
              duration: 1, // Duration of the bounce
              repeat: Infinity, // Infinite bounce effect
              repeatType: "reverse", // Reverse the bounce
              ease: "easeInOut", // Smooth easing for bounce
            }}
            onClick={scrollToMore}
            style={{
              position: "absolute",
              bottom: "20px",
              left: "50%",
              transform: "translateX(-50%)",
              cursor: "pointer",
              color: "#007bff",
              fontSize: "1rem",
            }}
          >
            <span>Scroll</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="2"
              stroke="currentColor"
              style={{ width: "20px", height: "20px", marginLeft: "5px" }}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15.75 17.25 12 21m0 0-3.75-3.75M12 21V3"
              />
            </svg>
          </motion.div>
        )}
      </Container>
    </>
  );
};

export default SwipeableBlogs;
