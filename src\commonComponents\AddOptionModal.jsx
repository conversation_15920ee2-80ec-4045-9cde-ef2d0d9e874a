import React, { useState, useRef, useEffect } from "react";
import { Modal, Button, Form } from "react-bootstrap";
import { useDispatch } from "react-redux";
import { toast } from "react-hot-toast";
import imageCompression from "browser-image-compression";
import { createOption } from "../redux/slice/optionsSlice";
import MathEditor from "./MathEditor";
import MathTextRenderer from "./MathTextRenderer";
import RichTextEditor from "./RichTextEditor";

const AddOptionModal = ({ show, onHide, questionSlug, onOptionAdded }) => {
  const dispatch = useDispatch();
  
  // Option form state
  const [optionFormData, setOptionFormData] = useState({
    optionText: "",
    isCorrect: false,
  });
  
  // Option image state
  const [optionImage, setOptionImage] = useState(null);
  const [optionImagePreview, setOptionImagePreview] = useState(null);
  const [optionImageError, setOptionImageError] = useState("");
  const [optionImageSizeText, setOptionImageSizeText] = useState("");
  const [isCheckingOptionImage, setIsCheckingOptionImage] = useState(false);
  const [resetOptionImageInput, setResetOptionImageInput] = useState(false);
  const optionImageInputRef = useRef(null);
  
  // Math editor state
  const [localMathContent, setLocalMathContent] = useState("");
  const [showOptionMathEditor, setShowOptionMathEditor] = useState(false);
  
  // Submission state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset file input when resetOptionImageInput changes
  useEffect(() => {
    if (resetOptionImageInput && optionImageInputRef.current) {
      optionImageInputRef.current.value = "";
    }
  }, [resetOptionImageInput]);

  // Handle option input change
  const handleOptionInputChange = (e) => {
    const { name, value } = e.target;
    setOptionFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle option image change
  const handleOptionImageChange = (e) => {
    const file = e.target.files[0];

    if (!file) return;

    setIsCheckingOptionImage(true);
    setOptionImageError("");
    setOptionImage(null);
    setOptionImagePreview(null);
    setOptionImageSizeText("");

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 200 * 1024) {
      // Directly set the image if it is small enough
      setOptionImage(file);
      const reader = new FileReader();
      reader.onload = () => setOptionImagePreview(reader.result);
      reader.readAsDataURL(file);
      setIsCheckingOptionImage(false);
      setOptionImageSizeText(`Image size: ${originalSizeKB} KB`);
    } else {
      const options = {
        maxSizeMB: 0.2,
        maxWidthOrHeight: 300,
        useWebWorker: true,
      };

      try {
        imageCompression(file, options)
          .then((compressedFile) => {
            const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);

            if (compressedFile.size <= 200 * 1024) {
              // Convert Blob to File (required for form data)
              const fileName = "compressed_option_" + file.name;
              const compressedFileAsFile = new File(
                [compressedFile],
                fileName,
                {
                  type: compressedFile.type,
                }
              );

              setOptionImage(compressedFileAsFile);

              const reader = new FileReader();
              reader.onload = () => setOptionImagePreview(reader.result);
              reader.readAsDataURL(compressedFileAsFile);

              setOptionImageSizeText(
                `Original Size: ${originalSizeKB} KB, Compressed Size: ${compressedSizeKB} KB`
              );
            } else {
              setOptionImageError(
                `Image exceeds 200KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`
              );
            }
          })
          .catch((error) => {
            console.error("Option image compression failed:", error);
            setOptionImageError("An error occurred while compressing the image.");
          })
          .finally(() => {
            setIsCheckingOptionImage(false);
          });
      } catch (error) {
        console.error("Error handling option image change:", error);
        setOptionImageError("An error occurred while processing the image.");
        setIsCheckingOptionImage(false);
      }
    }
  };



  // Handle add option
  const handleAddOption = async () => {
    setIsSubmitting(true);

    if (!questionSlug) {
      toast.error("Question slug is required");
      setIsSubmitting(false);
      return;
    }

    if (!optionFormData.optionText.trim()) {
      toast.error("Option text is required");
      setIsSubmitting(false);
      return;
    }

    const optionPayload = {
      option_text: optionFormData.optionText,
      is_correct: optionFormData.isCorrect === "true",
      ...(optionImage && { attachments: optionImage }),
    };

    try {
      await dispatch(
        createOption({ payload: optionPayload, questionSlug })
      ).unwrap();
      
      toast.success("Option added successfully!");
      
      // Reset form
      setOptionFormData({
        optionText: "",
        isCorrect: false,
      });
      
      // Reset option image states
      setOptionImage(null);
      setOptionImagePreview(null);
      setOptionImageError("");
      setOptionImageSizeText("");
      setResetOptionImageInput(prev => !prev);
      
      // Call callback if provided
      if (onOptionAdded) {
        onOptionAdded();
      }
      
    } catch (error) {
      toast.error(error.message || "Failed to add option");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    // Reset all states
    setOptionFormData({
      optionText: "",
      isCorrect: false,
    });
    setOptionImage(null);
    setOptionImagePreview(null);
    setOptionImageError("");
    setOptionImageSizeText("");
    setLocalMathContent("");
    setShowOptionMathEditor(false);

    onHide();
  };

  return (
    <>
      <Modal show={show} onHide={handleClose} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Add Option to Question</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {/* Option Text */}
          <Form.Group className="mb-3">
            <RichTextEditor
              label="Option Text"
              name="optionText"
              value={optionFormData.optionText}
              onChange={handleOptionInputChange}
              placeholder="Enter option text"
              rows={3}
              required
            />
          </Form.Group>

          {/* Math Editor Section for Options */}
          <Form.Group className="mb-3">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <Form.Label>Mathematical Content (Optional)</Form.Label>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={() => setShowOptionMathEditor(!showOptionMathEditor)}
              >
                {showOptionMathEditor ? 'Hide' : 'Add'} Math
              </Button>
            </div>

            {showOptionMathEditor && (
              <MathEditor
                value={localMathContent}
                onChange={setLocalMathContent}
                label="Mathematical Expression"
                placeholder="Enter mathematical expressions for this option..."
                showPreview={true}
                showRawLatex={false}
                displayMode={false}
                embeddedMode={true}
                textContent={optionFormData.optionText}
                onTextContentChange={(newText) => handleOptionInputChange({
                  target: { name: 'optionText', value: newText }
                })}
                className="mb-2"
              />
            )}

            {/* Preview of option text with embedded math */}
            {optionFormData.optionText && (
              <div className="mt-2">
                <Form.Label>Option Preview:</Form.Label>
                <div className="border rounded p-2 bg-light small">
                  <MathTextRenderer text={optionFormData.optionText} />
                </div>
              </div>
            )}
          </Form.Group>



          {/* Is Correct */}
          <Form.Group className="mb-3">
            <Form.Label>Is this the correct answer?</Form.Label>
            <Form.Select
              name="isCorrect"
              value={optionFormData.isCorrect}
              onChange={handleOptionInputChange}
              required
            >
              <option value={false}>No</option>
              <option value={true}>Yes</option>
            </Form.Select>
          </Form.Group>

          {/* Option Image */}
          <Form.Group className="mb-3">
            {optionImageSizeText && (
              <p className="text-success">{optionImageSizeText}</p>
            )}
            {optionImageError && (
              <p className="text-danger mb-2">{optionImageError}</p>
            )}
            <Form.Label>Option Image (Optional, Under 200 KB)</Form.Label>
            <Form.Control
              ref={optionImageInputRef}
              type="file"
              accept="image/*"
              onChange={handleOptionImageChange}
              disabled={isCheckingOptionImage}
            />
            {isCheckingOptionImage && (
              <p className="text-info small mt-1">Processing image...</p>
            )}
          </Form.Group>

          {/* Image Preview */}
          {optionImagePreview && (
            <div className="mb-3">
              <Form.Label>Image Preview</Form.Label>
              <div>
                <img
                  src={optionImagePreview}
                  alt="Option Preview"
                  style={{
                    width: "100%",
                    maxHeight: "150px",
                    objectFit: "cover",
                    border: "1px solid #ddd",
                    borderRadius: "4px"
                  }}
                />
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            variant="success"
            onClick={handleAddOption}
            disabled={isSubmitting || isCheckingOptionImage}
          >
            {isSubmitting ? 'Adding Option...' : 'Add Option'}
          </Button>
        </Modal.Footer>
      </Modal>

    </>
  );
};

export default AddOptionModal;
