import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor; 
  return accessToken;
};

// Create a tier
export const createTier = createAsyncThunk(
  'tiers/create',
  async ({ data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TIER_ENDPOINT}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to create tier');
    }
  }
);

// Get all tiers
export const getAllTiers = createAsyncThunk(
  'tiers/getAll',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TIER_ENDPOINT}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch tiers');
    }
  }
);

// Get a specific tier
export const getTier = createAsyncThunk(
  'tiers/get',
  async ( tierSlug , { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TIER_ENDPOINT}${tierSlug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch tier');
    }
  }
);

// Update a tier
export const updateTier = createAsyncThunk(
  'tiers/update',
  async ({ slug, data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TIER_ENDPOINT}${slug}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to update tier');
    }
  }
);

// Delete a tier
export const deleteTier = createAsyncThunk(
  'tiers/delete',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_TIER_ENDPOINT}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return slug; // Return the slug to remove the tier from the store
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to delete tier');
    }
  }
);

const tierSlice = createSlice({
  name: 'tiers',
  initialState: { searchTerm: '', loading: false, error: null },
  reducers: {
    setSearchTerm: (state, action) => {
      state.searchTerm = action.payload; // Set search term from input
    },
  },
  extraReducers: (builder) => {
    builder
      // Create tier
      .addCase(createTier.pending, (state) => {
        state.loading = true;
      })
      .addCase(createTier.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(createTier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get all tiers
      .addCase(getAllTiers.pending, (state) => {
        state.loading = true;
      })
      .addCase(getAllTiers.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(getAllTiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get a specific tier
      .addCase(getTier.pending, (state) => {
        state.loading = true;
      })
      .addCase(getTier.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(getTier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update a tier
      .addCase(updateTier.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateTier.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(updateTier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete a tier
      .addCase(deleteTier.pending, (state) => {
        state.loading = true;
      })
      .addCase(deleteTier.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(deleteTier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default tierSlice.reducer;
