import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form, Spinner } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getTestPatterns } from "../../redux/slice/paperEngineSlice"; // Redux action to fetch test patterns

const EditPaperModal = ({ show, onHide, editData, onEditChange, onSubmit }) => {
  const dispatch = useDispatch();
  const { testPatterns, status: testPatternsStatus } = useSelector(
    (state) => state.paperEngine
  ); // Access test patterns from Redux store

  const [testPatternValue, setTestPatternValue] = useState(""); // Local state for selected test pattern

  // Fetch test patterns when the modal opens
  useEffect(() => {
    if (testPatternsStatus === "idle") {
      dispatch(getTestPatterns()); // Fetch test patterns if not already fetched
    }
  }, [dispatch, testPatternsStatus, show]);

  // Prefill the test pattern based on the editData's test_pattern ID
  useEffect(() => {
    if (editData && editData.test_pattern !== undefined) {
      setTestPatternValue(editData.test_pattern); // Prefill with the test pattern ID from the paper
    }
  }, [editData]);

  const handleTestPatternChange = (e) => {
    setTestPatternValue(e.target.value); // Update selected test pattern
  };

  const handleSubmit = (e) => {
    e.preventDefault(); // Prevent default form submission

    const updatedData = {
      ...editData,
      test_pattern: testPatternValue || null, // Set to null if "None" is selected
    };

    onSubmit(updatedData); // Call the onSubmit function passed from the parent
  };

  // Find the selected test pattern name from the list
  const selectedTestPattern = testPatterns.find(
    (pattern) => pattern.pattern_id === testPatternValue
  );

  // Fallback for editData if not available
  const { name = "", description = "", max_marks = 0, duration = "", test_pattern_details = "" } = editData || {};

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Paper</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {(!editData || !editData.name) ? (
          <h5>No paper selected for editing.</h5>
        ) : (
          <Form onSubmit={handleSubmit}>
            <Form.Group className="mb-3">
              <Form.Label>Paper Name</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={name}
                onChange={onEditChange}
                placeholder="Enter paper name"
                required
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Paper Description</Form.Label>
              <Form.Control
                type="text"
                name="description"
                value={description}
                onChange={onEditChange}
                placeholder="Enter paper description"
                required
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Max Marks</Form.Label>
              <Form.Control
                type="number"
                name="max_marks"
                value={max_marks}
                onChange={onEditChange}
                placeholder="Enter max marks"
                required
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Duration</Form.Label>
              <Form.Control
                type="text"
                name="duration"
                value={duration}
                onChange={onEditChange}
                placeholder="Enter duration"
                required
              />
            </Form.Group>

            <h6 className="mb-3">
              {test_pattern_details
                ? `Current Test Pattern: ${test_pattern_details.name} (Version: ${test_pattern_details.version})`
                : "No test pattern is selected"}
            </h6>

            <Form.Group className="mb-3">
              <Form.Label>Test Pattern</Form.Label>
              {testPatternsStatus === "loading" ? (
                <Spinner animation="border" variant="primary" />
              ) : (
                <Form.Control
                  as="select"
                  value={testPatternValue}
                  onChange={handleTestPatternChange}
                >
                  <option value="">None</option> {/* Option for "None" */}
                  {testPatterns.map((pattern) => (
                    <option key={pattern.pattern_id} value={pattern.pattern_id}>
                      {pattern.name} (Version: {pattern.version})
                    </option>
                  ))}
                </Form.Control>
              )}
            </Form.Group>

            <Button variant="outline-primary w-100" type="submit">
              Save Changes
            </Button>
          </Form>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default EditPaperModal;
