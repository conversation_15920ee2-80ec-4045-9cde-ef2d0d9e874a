import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Modal } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
// import defaultImage from "../../assets/blog.png";
import { useDispatch } from "react-redux";
import Swal from "sweetalert2";
import { deleteBlog, updateBlog } from "../../redux/slice/blogSlice";
import BlogForm from "./BlogForm"; // Import BlogForm
import toast from "react-hot-toast";
import EditBlogForm from "./EditBlogForm";
// import UpdateBlogForm from "./UpdateBlogForm";

const PublicBlogCard = ({ blog, fetchBlogs }) => {
  const dispatch = useDispatch();
  const [showModal, setShowModal] = useState(false); // State to control the modal visibility
  const [selectedBlog, setSelectedBlog] = useState(null); // State to store the selected blog for editing
  const [loading, setLoading] = useState(false); // Loading state for form submission
  const baseUrl = import.meta.env.VITE_BASE_URL;
  const defaultImage = "../../../../assets/deafult.jpeg";
  // Function to handle blog deletion

  //   console.log("BLOG: ", blog);

  const handleDelete = async () => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        // Dispatch deleteBlog action with blog.id
        await dispatch(deleteBlog(blog.id));
        Swal.fire("Deleted!", "Your blog has been deleted.", "success");
        fetchBlogs(); // Call fetchBlogs to update the list
      } catch (error) {
        Swal.fire(
          "Error!",
          "Something went wrong while deleting the blog.",
          "error"
        );
      }
    }
  };

  // Function to handle the Edit button click
  const handleEdit = () => {
    console.log("Editing blog: ", blog);
    setSelectedBlog(blog); // Set the selected blog for editing
    setShowModal(true); // Show the modal
  };

  // Function to close the modal
  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedBlog(null); // Clear the selected blog when closing the modal
  };

  const handleSubmit = async (updatedBlogData) => {
    // Validate required fields before submission
    if (
      !updatedBlogData.title ||
      !updatedBlogData.author ||
      !updatedBlogData.content
    ) {
      toast.error("Title, Author, and Content are required.");
      return;
    }
  
    const defaultImage = `https://${import.meta.env.VITE_WEBSITE_DOMAIN}/images/default.png`;
    const canonicalUrl = `https://${import.meta.env.VITE_WEBSITE_DOMAIN}/blog/${updatedBlogData.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-|-$/g, "")}`;
  
    let uploadedImageUrl = defaultImage;

    console.log("uploadedBlogData inside BlogCard submit: ", updatedBlogData);
    
    if (updatedBlogData.image) {
      if (updatedBlogData.image instanceof File) {
        console.log("Image is file: ", updatedBlogData.image);
        
        const fileName = updatedBlogData.image.name.replace(/\s+/g, "-").toLowerCase();
        const fileExtension = fileName.split(".").pop(); 
        uploadedImageUrl = `https://${import.meta.env.VITE_WEBSITE_DOMAIN}/media/blog_images/${fileName}`;
        updatedBlogData.image = `/media/blog_images/${fileName}`;
      } else if (typeof updatedBlogData.image === "string") {
        console.log("image is string: ", updatedBlogData.image);
        
        // If it's a string (URL), use it as-is
        uploadedImageUrl = `https://${import.meta.env.VITE_WEBSITE_DOMAIN}${updatedBlogData.image}`;
      }
    } 
    // else {
    //   console.log("Image is undefined: ", updatedBlogData.image);
      
    //   uploadedImageUrl = selectedBlog.image ? selectedBlog.image : defaultImage;
    // }
  console.log("uploadedImageUrl: ", uploadedImageUrl);
  
    const openGraph = {
      "og:title": updatedBlogData.meta_title || updatedBlogData.title,
      "og:description":
        updatedBlogData.meta_description || updatedBlogData.introduction,
      "og:image": uploadedImageUrl,
    };
  
    const twitterCards = {
      "twitter:title": updatedBlogData.meta_title || updatedBlogData.title,
      "twitter:description":
        updatedBlogData.meta_description || updatedBlogData.introduction,
      "twitter:image": uploadedImageUrl,
    };
  
    const breadcrumbSchema = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          name: "Home",
          item: `https://${import.meta.env.VITE_WEBSITE_DOMAIN}/`,
        },
        {
          "@type": "ListItem",
          position: 2,
          name: "Blog",
          item: `https://${import.meta.env.VITE_WEBSITE_DOMAIN}/blog`,
        },
        {
          "@type": "ListItem",
          position: 3,
          name: updatedBlogData.title,
          item: canonicalUrl,
        },
      ],
    };
  
    const articleSchema = {
      "@context": "https://schema.org",
      "@type": "Article",
      headline: updatedBlogData.meta_title || updatedBlogData.title,
      image: [uploadedImageUrl],
      author: updatedBlogData.author || "Anonymous",
      publisher: {
        "@type": "Organization",
        name: "Your Website",
        logo: {
          "@type": "ImageObject",
          url: `https://${import.meta.env.VITE_WEBSITE_DOMAIN}/images/logo.png`,
        },
      },
      datePublished: new Date().toISOString(),
      dateModified: new Date().toISOString(),
    };
  
    const blogData = {
      ...updatedBlogData,
      canonical_url: canonicalUrl,
      open_graph: JSON.stringify(openGraph),
      twitter_cards: JSON.stringify(twitterCards),
      breadcrumb_schema: JSON.stringify(breadcrumbSchema),
      article_schema: JSON.stringify(articleSchema),
    };
  
  
    console.log("submitted clicked: ", updatedBlogData);
  
    setLoading(true);
    try {
      await dispatch(
        updateBlog({ id: selectedBlog.id, updatedData: blogData })
      ).unwrap();
      toast.success("Blog updated successfully!");
      fetchBlogs(); // Refresh the list after update
    } catch (error) {
      toast.error(error.message || "Error updating blog.");
    } finally {
      setLoading(false);
    }
  };
  

  return (
    <div className="d-flex flex-column">
      <Card className="mb-4">
        <Card.Img
          variant="top"
          src={`${baseUrl}${blog.image ? blog.image : defaultImage}`}
          alt={blog.image_caption || "Blog Image"}
        />
        <Card.Body>
          <Card.Title>{blog.title}</Card.Title>
          <Card.Text>
            <small className="text-muted">
              Published on: {new Date(blog.published_date).toLocaleDateString()}
            </small>
          </Card.Text>
          <Card.Subtitle className="my-1 text-muted">
            By {blog.author_first_name} {blog.author_last_name}
          </Card.Subtitle>
          <Card.Text>{blog.meta_description}</Card.Text>

          <div className="d-flex justify-content-center mt-2">
            {/* View Button */}
            <Link to={`/blog/${blog.id}`}>
              <Button variant="outline-success" className="mx-1">
                View
              </Button>
            </Link>

            {/* Edit Button */}
            <Button
              variant="outline-primary"
              className="mx-1"
              onClick={handleEdit}
            >
              Edit
            </Button>

            {/* Delete Button */}
            <Button variant="outline-danger" onClick={handleDelete}>
              Delete
            </Button>
          </div>
        </Card.Body>
      </Card>

      {/* Modal for Editing Blog */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Edit Blog</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedBlog && (
            <>
              {/* BlogForm for editing */}
              <EditBlogForm
                initialValues={{
                  ...selectedBlog,
                  //   image: selectedBlog.image ? `${baseUrl}${selectedBlog.image}` : defaultImage,
                }}
                onSubmit={handleSubmit} // Pass handleSubmit for form submission
                loading={loading} // Set loading state as needed
              />
            </>
          )}
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default PublicBlogCard;
