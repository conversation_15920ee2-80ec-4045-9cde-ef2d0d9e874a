import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON>, <PERSON><PERSON>, Alert, Badge, Spinner } from 'react-bootstrap';
import { getSOP } from '../Redux/slice/sopSlice';

const SOPDebugger = () => {
  const dispatch = useDispatch();
  const { sops, isLoading, error } = useSelector((state) => state.sop);
  const { accessToken } = useSelector((state) => state.contributor);

  useEffect(() => {
    console.log('SOPDebugger: Component mounted');
  }, []);

  const handleFetchSOPs = () => {
    console.log('SOPDebugger: Fetching SOPs manually...');
    dispatch(getSOP());
  };

  const testPDFUrl = (url) => {
    console.log('SOPDebugger: Testing PDF URL:', url);
    window.open(url, '_blank');
  };

  return (
    <div className="container mt-4">
      <Card>
        <Card.Header>
          <h4>SOP System Debugger</h4>
        </Card.Header>
        <Card.Body>
          {/* Authentication Status */}
          <div className="mb-4">
            <h5>Authentication Status</h5>
            <p>
              <strong>Access Token:</strong>{' '}
              <Badge bg={accessToken ? 'success' : 'danger'}>
                {accessToken ? 'Present' : 'Missing'}
              </Badge>
            </p>
            {accessToken && (
              <p className="text-muted small">
                Token: {accessToken.substring(0, 20)}...
              </p>
            )}
          </div>

          {/* API Configuration */}
          <div className="mb-4">
            <h5>API Configuration</h5>
            <p><strong>Base URL:</strong> {import.meta.env.VITE_BASE_URL}</p>
            <p><strong>SOP Endpoint:</strong> {import.meta.env.VITE_SOP}</p>
            <p><strong>Full API URL:</strong> {`${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SOP}`}</p>
          </div>

          {/* Fetch SOPs */}
          <div className="mb-4">
            <h5>Fetch SOPs</h5>
            <Button 
              variant="primary" 
              onClick={handleFetchSOPs}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Spinner size="sm" className="me-2" />
                  Fetching...
                </>
              ) : (
                'Fetch SOPs'
              )}
            </Button>
          </div>

          {/* Loading State */}
          {isLoading && (
            <Alert variant="info">
              <Spinner size="sm" className="me-2" />
              Loading SOPs from API...
            </Alert>
          )}

          {/* Error State */}
          {error && (
            <Alert variant="danger">
              <Alert.Heading>Error</Alert.Heading>
              <p><strong>Error:</strong> {JSON.stringify(error, null, 2)}</p>
            </Alert>
          )}

          {/* SOPs Data */}
          {sops && sops.length > 0 && (
            <div className="mb-4">
              <h5>SOPs Data ({sops.length} found)</h5>
              {sops.map((sop, index) => (
                <Card key={sop.id} className="mb-3">
                  <Card.Body>
                    <h6>SOP #{index + 1}</h6>
                    <p><strong>ID:</strong> {sop.id}</p>
                    <p><strong>Name:</strong> {sop.name}</p>
                    <p><strong>Access:</strong> {sop.access} ({sop.access_display})</p>
                    <p><strong>PDF URL:</strong> 
                      <br />
                      <code className="text-break">{sop.pdf}</code>
                    </p>
                    <p><strong>Created:</strong> {new Date(sop.created).toLocaleString()}</p>
                    <p><strong>Last Update:</strong> {new Date(sop.last_update).toLocaleString()}</p>
                    
                    <div className="mt-3">
                      <Button 
                        variant="outline-primary" 
                        size="sm"
                        onClick={() => testPDFUrl(sop.pdf)}
                        className="me-2"
                      >
                        Test PDF URL
                      </Button>
                      <Button 
                        variant="outline-secondary" 
                        size="sm"
                        onClick={() => console.log('SOP Object:', sop)}
                      >
                        Log to Console
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              ))}
            </div>
          )}

          {/* No SOPs */}
          {sops && sops.length === 0 && !isLoading && !error && (
            <Alert variant="info">
              <Alert.Heading>No SOPs Found</Alert.Heading>
              <p>The API returned an empty array. No SOPs are available.</p>
            </Alert>
          )}

          {/* Raw Redux State */}
          <div className="mb-4">
            <h5>Raw Redux State</h5>
            <pre className="bg-light p-3 rounded">
              {JSON.stringify({ sops, isLoading, error }, null, 2)}
            </pre>
          </div>

          {/* Instructions */}
          <Alert variant="secondary">
            <Alert.Heading>How to Use This Debugger</Alert.Heading>
            <ol>
              <li>Check that you have a valid access token</li>
              <li>Verify the API configuration URLs are correct</li>
              <li>Click "Fetch SOPs" to test the API call</li>
              <li>If SOPs are found, test the PDF URLs</li>
              <li>Check the browser console for detailed logs</li>
              <li>Use the raw Redux state to see exactly what data is stored</li>
            </ol>
          </Alert>
        </Card.Body>
      </Card>
    </div>
  );
};

export default SOPDebugger;
