import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON>, Button, Alert, Spinner } from 'react-bootstrap';
import { createMasterQuestion } from '../../redux/masterQuestionSlice';

const MasterQuestionForm = () => {
  const dispatch = useDispatch();
  const { isLoading, error, question } = useSelector((state) => state.masterQuestion);

  const [formData, setFormData] = useState({
    title: '',
    passage_content: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    dispatch(createMasterQuestion(formData));
  };

  return (
    <div className="container mt-5">
      <h2>Create Master Question</h2>
      <Form onSubmit={handleSubmit}>
        <Form.Group className="mb-3">
          <Form.Label>Title</Form.Label>
          <Form.Control
            type="text"
            placeholder="Enter title"
            name="title"
            value={formData.title}
            onChange={handleChange}
          />
        </Form.Group>
        <Form.Group className="mb-3">
          <Form.Label>Passage Content</Form.Label>
          <Form.Control
            as="textarea"
            rows={5}
            placeholder="Enter passage content"
            name="passage_content"
            value={formData.passage_content}
            onChange={handleChange}
          />
        </Form.Group>
        <Button variant="primary" type="submit" disabled={isLoading}>
          {isLoading ? <Spinner animation="border" size="sm" /> : 'Submit'}
        </Button>
      </Form>
      {error && <Alert variant="danger" className="mt-3">{error}</Alert>}
      {question && <Alert variant="success" className="mt-3">Question created successfully!</Alert>}
    </div>
  );
};

export default MasterQuestionForm;
