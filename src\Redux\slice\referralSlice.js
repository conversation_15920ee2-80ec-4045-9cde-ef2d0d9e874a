// src/slices/referralSlice.js

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Async thunks for the functions (you'll replace these with actual API calls)
export const createReferral = createAsyncThunk('referral/createReferral', async (userData) => {
  // Simulate an API call to create a referral
  const response = await fetch('/api/create-referral', {
    method: 'POST',
    body: JSON.stringify(userData),
  });
  const data = await response.json();
  return data;
});

export const signupUsingReferral = createAsyncThunk('referral/signupUsingReferral', async (signupData) => {
  // Simulate an API call to sign up using the referral code
  const response = await fetch('/api/signup', {
    method: 'POST',
    body: JSON.stringify(signupData),
  });
  const data = await response.json();
  return data;
});

export const verifyOTP = createAsyncThunk('referral/verifyOTP', async (otpData) => {
  // Simulate an API call to verify OTP
  const response = await fetch('/api/verify-otp', {
    method: 'POST',
    body: JSON.stringify(otpData),
  });
  const data = await response.json();
  return data;
});

// Initial state
const initialState = {
  referralCode: '',
  otp: '',
  status: 'idle', // 'idle' | 'loading' | 'succeeded' | 'failed'
  error: null,
  referralData: null,
  otpVerified: false,
};

const referralSlice = createSlice({
  name: 'referral',
  initialState,
  reducers: {
    setReferralCode(state, action) {
      state.referralCode = action.payload;
    },
    setOTP(state, action) {
      state.otp = action.payload;
    },
    resetReferralState(state) {
      state.referralCode = '';
      state.otp = '';
      state.otpVerified = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createReferral.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(createReferral.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.referralData = action.payload;
      })
      .addCase(createReferral.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      .addCase(signupUsingReferral.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(signupUsingReferral.fulfilled, (state, action) => {
        state.status = 'succeeded';
        // Add more user data as needed
      })
      .addCase(signupUsingReferral.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      .addCase(verifyOTP.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(verifyOTP.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.otpVerified = true;
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      });
  },
});

export const { setReferralCode, setOTP, resetReferralState } = referralSlice.actions;

export default referralSlice.reducer;
