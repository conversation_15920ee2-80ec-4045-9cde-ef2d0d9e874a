import React, { useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { Row, Col, Card, Button, Container, Form, Pagination, Modal, Dropdown } from "react-bootstrap";
import { deleteSubTopic, updateSubTopic } from "../../redux/slice/subTopicSlice.js";
import { getSingleTopic } from "../../redux/slice/topicsSlice.js";
import AddSubTopic from "../components/AddSubTopic.jsx";
import NavigationBar from "../../commonComponents/NavigationBar.jsx";
import Swal from "sweetalert2";
import toast, { Toaster } from "react-hot-toast";

const ViewTopic = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { topic_slug } = useParams();
  const accessToken = useSelector((state) => state.contributor.accessToken || null);

  useEffect(() => {
    if (!accessToken) {
      toast.error("Please log in as a contributor!");
      const timer = setTimeout(() => {
        navigate("/contributor_login");
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [accessToken, navigate]);

  useEffect(() => {
    if (topic_slug && accessToken) {
      dispatch(getSingleTopic(topic_slug)); // Load the topic data with subtopics
    }
  }, [dispatch, topic_slug, accessToken]);

  const { topic, isLoading, error } = useSelector((state) => state.topic);
  const subTopics = topic?.data?.subtopics || [];

  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [subTopicsPerPage, setSubTopicsPerPage] = useState(6); // Default to 6 per page
  const [dropdownTitle, setDropdownTitle] = useState("5 per page"); // Default dropdown title
  const [selectedSubTopic, setSelectedSubTopic] = useState(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [viewModalOpen, setViewModalOpen] = useState(false);

  const currentSubTopics = useMemo(() => {
    const filtered = subTopics.filter((subTopic) =>
      subTopic.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      subTopic.description.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const indexOfLastSubTopic = currentPage * subTopicsPerPage;
    const indexOfFirstSubTopic = indexOfLastSubTopic - subTopicsPerPage;

    return filtered.slice(indexOfFirstSubTopic, indexOfLastSubTopic);
  }, [subTopics, searchQuery, currentPage, subTopicsPerPage]);

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1);
  };

  const handleDropdownSelect = (value) => {
    setSubTopicsPerPage(value === "all" ? subTopics.length : parseInt(value, 10));
    setDropdownTitle(value === "all" ? "All" : `${value} per page`);
    setCurrentPage(1); // Reset to the first page
  };

  const handleDeleteSubTopic = async (slug) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deleteSubTopic({ slug }));
        toast.success("SubTopic deleted successfully!");
        dispatch(getSingleTopic(topic_slug)); // Load the topic data with subtopics
      } catch (error) {
        console.error("Error deleting subtopic", error);
        toast.error("Failed to delete the subtopic. Please try again.");
      }
    }
  };

  const handleEditSubTopic = (subTopic) => {
    setSelectedSubTopic(subTopic);
    dispatch(getSingleTopic(topic_slug)); // Load the topic data with subtopics
    setEditModalOpen(true);
  };

  const handleSaveSubTopic = async () => {
    if (selectedSubTopic) {
      try {
        const response = await dispatch(
          updateSubTopic({
            topic_slug,
            slug: selectedSubTopic.slug,
            updatedData: {
              topic: topic_slug,
              name: selectedSubTopic.name,
              description: selectedSubTopic.description,
            },
          })
        );

        console.log("RESPONSE", response);

        //  Check if the response status is 200
        if (response?.meta?.requestStatus === "fulfilled") {
          setEditModalOpen(false);
          setSelectedSubTopic(null);
          dispatch(getSingleTopic(topic_slug)); // Load the topic data with subtopics
          toast.success("SubTopic updated successfully!");
        } else {
          toast.error("Failed to update the subtopic.");
        }
      } catch (error) {
        console.error("Error updating subtopic", error);
        toast.error("Failed to update the subtopic. Please try again.");
      }
    }
  };

  const handleViewSubTopic = (subTopic) => {
    setSelectedSubTopic(subTopic);
    setViewModalOpen(true);
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  if (isLoading) {
    return (
      <div className="text-center mt-5">
        <div className="spinner-border text-success" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-danger mt-5">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <>
      <NavigationBar />
      <Container>
        <Row>
          <Col md={3}>
            <AddSubTopic
              style={{ margin: "1rem" }}
              slug={topic_slug}
              onSubTopicAdded={() => dispatch(getSingleTopic(topic_slug))}
            />
          </Col>
          <Col md={9}>
            <div className="view-topic-container" style={{ flex: 1 }}>
              <h2 className="text-center text-success mt-3 mb-2" style={{ fontSize: "1.9rem" }}>
                Topic / SubTopics, <small className="h4"> See live updated here. </small>
              </h2>

              <Row className="mb-1 justify-content-center">
                <Col xs={12} sm={8} md={6} lg={6}>
                  <Card className="shadow-lg rounded-3 mb-3">
                    <Card.Body>
                      <Card.Title className="text-success">{topic?.name}</Card.Title>
                      <Card.Text>{topic?.description}</Card.Text>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>

              <Row className="mb-4 justify-content-center ">
                <Col xs={12} sm={8} md={6} lg={5} className="d-flex justify-content-between">
                  <Form.Control
                    type="text"
                    placeholder="Search subtopics..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                  />
                  <Dropdown onSelect={handleDropdownSelect}>
                    <Dropdown.Toggle variant="success" style={{ margin: "0rem 0.2rem" }}>
                      {dropdownTitle}
                    </Dropdown.Toggle>
                    <Dropdown.Menu>
                      <Dropdown.Item eventKey="5">5 per page</Dropdown.Item>
                      <Dropdown.Item eventKey="25">25 per page</Dropdown.Item>
                      <Dropdown.Item eventKey="50">50 per page</Dropdown.Item>
                      <Dropdown.Item eventKey="100">100 per page</Dropdown.Item>
                      <Dropdown.Item eventKey="all">All</Dropdown.Item>
                    </Dropdown.Menu>
                  </Dropdown>
                </Col>
              </Row>

              <Row className="d-flex flex-wrap mx-2">
                {currentSubTopics.length > 0 ? (
                  currentSubTopics.map((subTopic) => (
                    <Col key={subTopic.slug} xs={12} sm={6} md={6} lg={4}>
                      <Card className="mb-4 shadow-lg rounded-3">
                        <Card.Body>
                          <Card.Title className="text-success text-truncate">{subTopic.name}</Card.Title>
                          <Card.Text className="text-truncate">{subTopic.description || "No description available"}</Card.Text>
                          <div className="d-flex justify-content-evenly mt-2">
                            <Button variant="outline-success" onClick={() => handleEditSubTopic(subTopic)}>
                              Edit
                            </Button>
                            <Button variant="outline-primary" onClick={() => handleViewSubTopic(subTopic)}>
                              View
                            </Button>
                            <Button variant="outline-danger" onClick={() => handleDeleteSubTopic(subTopic.slug)}>
                              Delete
                            </Button>
                          </div>
                        </Card.Body>
                      </Card>
                    </Col>
                  ))
                ) : (
                  <div className="col-12 text-center text-muted">
                    No subtopics available for this topic.
                  </div>
                )}
              </Row>

              {subTopics.length > subTopicsPerPage && (
                <div className="d-flex justify-content-center mt-1">
                  <Pagination>
                    <Pagination.First onClick={() => handlePageChange(1)} disabled={currentPage === 1} />
                    <Pagination.Prev onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1} />
                    {Array.from({ length: Math.ceil(subTopics.length / subTopicsPerPage) }).map((_, index) => (
                      <Pagination.Item
                        key={index + 1}
                        active={index + 1 === currentPage}
                        onClick={() => handlePageChange(index + 1)}
                      >
                        {index + 1}
                      </Pagination.Item>
                    ))}
                    <Pagination.Next
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === Math.ceil(subTopics.length / subTopicsPerPage)}
                    />
                    <Pagination.Last
                      onClick={() => handlePageChange(Math.ceil(subTopics.length / subTopicsPerPage))}
                      disabled={currentPage === Math.ceil(subTopics.length / subTopicsPerPage)}
                    />
                  </Pagination>
                </div>
              )}
            </div>
          </Col>
        </Row>
      </Container>

      {/* Edit Modal */}
      <Modal show={editModalOpen} onHide={() => setEditModalOpen(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit SubTopic</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group controlId="subTopicName">
            <Form.Label>SubTopic Name</Form.Label>
            <Form.Control
              type="text"
              value={selectedSubTopic?.name || ""}
              onChange={(e) => setSelectedSubTopic({ ...selectedSubTopic, name: e.target.value })}
            />
          </Form.Group>
          <Form.Group controlId="subTopicDescription" className="mt-2">
            <Form.Label>SubTopic Description</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              value={selectedSubTopic?.description || ""}
              onChange={(e) =>
                setSelectedSubTopic({ ...selectedSubTopic, description: e.target.value })
              }
            />
          </Form.Group>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setEditModalOpen(false)}>
            Close
          </Button>
          <Button variant="primary" onClick={handleSaveSubTopic}>
            Save Changes
          </Button>
        </Modal.Footer>
      </Modal>

      {/* View Modal */}
      <Modal show={viewModalOpen} onHide={() => setViewModalOpen(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>View SubTopic</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <h4>{selectedSubTopic?.name}</h4>
          <p>{selectedSubTopic?.description || "No description available."}</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setViewModalOpen(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      <Toaster />
    </>
  );
};

export default ViewTopic;
