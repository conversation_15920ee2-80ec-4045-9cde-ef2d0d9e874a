import React from 'react';
import { InlineMath, BlockMath } from 'react-katex';
import 'katex/dist/katex.min.css';

/**
 * Component to render text with embedded LaTeX math expressions
 * Supports both inline math ($...$) and display math ($$...$$)
 */
const MathTextRenderer = ({ 
  text = '', 
  className = '',
  fallbackToPlainText = true 
}) => {
  if (!text || typeof text !== 'string') {
    return <span className={className}>{text}</span>;
  }

  try {
    // Parse text for math expressions
    const parts = parseTextWithMath(text);

    // Debug logging for problematic text
    if (text.includes('∫') || text.includes('​')) {
      console.log('MathTextRenderer Debug:', {
        originalText: text,
        parsedParts: parts,
        containsIntegral: text.includes('∫'),
        containsZeroWidth: text.includes('​')
      });
    }

    if (parts.length === 1 && parts[0].type === 'text') {
      // No math found, return text with HTML formatting
      return (
        <span
          className={className}
          dangerouslySetInnerHTML={{ __html: text }}
        />
      );
    }

    return (
      <span className={className}>
        {parts.map((part, index) => {
          switch (part.type) {
            case 'text':
              return (
                <span
                  key={index}
                  dangerouslySetInnerHTML={{ __html: part.content }}
                />
              );
            case 'inline-math':
              try {
                return (
                  <InlineMath
                    key={index}
                    math={part.content}
                    errorColor="#dc3545"
                  />
                );
              } catch (mathError) {
                console.warn('Error rendering inline math:', part.content, mathError);
                return (
                  <span key={index} className="text-danger" title={`Math error: ${mathError.message}`}>
                    ${part.content}$
                  </span>
                );
              }
            case 'display-math':
              try {
                return (
                  <BlockMath
                    key={index}
                    math={part.content}
                    errorColor="#dc3545"
                  />
                );
              } catch (mathError) {
                console.warn('Error rendering display math:', part.content, mathError);
                return (
                  <div key={index} className="text-danger" title={`Math error: ${mathError.message}`}>
                    $${part.content}$$
                  </div>
                );
              }
            default:
              return (
                <span
                  key={index}
                  dangerouslySetInnerHTML={{ __html: part.content }}
                />
              );
          }
        })}
      </span>
    );
  } catch (error) {
    console.warn('Error rendering math in text:', error);
    if (fallbackToPlainText) {
      return (
        <span
          className={className}
          dangerouslySetInnerHTML={{ __html: text }}
        />
      );
    }
    return (
      <span className={`${className} text-danger`}>
        <small>Error rendering math content</small>
      </span>
    );
  }
};

/**
 * Parse text containing math expressions delimited by $ and $$
 * Returns array of objects with type and content
 */
function parseTextWithMath(text) {
  const parts = [];
  let currentIndex = 0;

  // Clean the text first to handle common issues
  const cleanedText = cleanTextForMath(text);

  // Regular expression to match $...$ and $$...$$ patterns
  const mathRegex = /(\$\$[\s\S]*?\$\$|\$[^$\n]*?\$)/g;
  let match;

  while ((match = mathRegex.exec(cleanedText)) !== null) {
    // Add text before the math expression
    if (match.index > currentIndex) {
      const textContent = cleanedText.slice(currentIndex, match.index);
      if (textContent) {
        parts.push({
          type: 'text',
          content: textContent
        });
      }
    }

    // Add the math expression
    const mathExpression = match[0];
    if (mathExpression.startsWith('$$') && mathExpression.endsWith('$$')) {
      // Display math
      const mathContent = mathExpression.slice(2, -2).trim();
      parts.push({
        type: 'display-math',
        content: cleanMathExpression(mathContent)
      });
    } else if (mathExpression.startsWith('$') && mathExpression.endsWith('$')) {
      // Inline math
      const mathContent = mathExpression.slice(1, -1).trim();
      parts.push({
        type: 'inline-math',
        content: cleanMathExpression(mathContent)
      });
    }

    currentIndex = match.index + match[0].length;
  }

  // Add remaining text after the last math expression
  if (currentIndex < cleanedText.length) {
    const remainingText = cleanedText.slice(currentIndex);
    if (remainingText) {
      parts.push({
        type: 'text',
        content: remainingText
      });
    }
  }

  // If no math expressions found, return the original text
  if (parts.length === 0) {
    parts.push({
      type: 'text',
      content: cleanedText
    });
  }

  return parts;
}

/**
 * Clean text before parsing to handle common issues
 */
function cleanTextForMath(text) {
  if (!text) return '';

  return text
    // Remove zero-width spaces and other invisible characters
    .replace(/[\u200B-\u200D\uFEFF]/g, '')

    // Fix common copy-paste issues from formatted text
    // Handle cases where superscripts become separate numbers
    .replace(/(\w+)\s*\n\s*(\d+)\s*\n/g, '$1^{$2} ')  // Handle line breaks in superscripts
    .replace(/(\w+)\s*\u2009\s*(\d+)/g, '$1^{$2}')  // Thin space before superscript
    .replace(/(\w+)\s*\u00A0\s*(\d+)/g, '$1^{$2}')  // Non-breaking space before superscript

    // Handle integral bounds that get separated by line breaks
    .replace(/∫\s*\n\s*(\d+)\s*\n\s*(\w+)/g, '∫_{$1}^{$2}')
    .replace(/∫\s+(\d+)\s+(\w+)\s*​/g, '∫_{$1}^{$2}')  // Zero-width space after bounds

    // Normalize whitespace (but preserve structure within math expressions)
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Clean and normalize math expressions for better rendering
 */
function cleanMathExpression(mathContent) {
  if (!mathContent) return '';

  return mathContent
    // Convert Unicode integral symbol to LaTeX
    .replace(/∫/g, '\\int')

    // Fix integral bounds patterns
    .replace(/\\int\s*(\d+)\s*(\w+)/g, '\\int_{$1}^{$2}')
    .replace(/∫\s*(\d+)\s*(\w+)/g, '\\int_{$1}^{$2}')

    // Fix common superscript patterns (when copy-pasted from formatted text)
    .replace(/(\w+)\s*(\d+)\s+dt/g, '$1^{$2} dt')  // t 2 dt → t^{2} dt
    .replace(/(\w+)\s*(\d+)\s+d(\w+)/g, '$1^{$2} d$3')  // t 2 dx → t^{2} dx
    .replace(/(\w+)\s*(\d+)\s*$/g, '$1^{$2}')  // t 2 at end → t^{2}
    .replace(/(\w+)\s*(\d+)\s*([+\-*/=])/g, '$1^{$2}$3')  // t 2 + → t^{2} +

    // Fix subscript patterns
    .replace(/(\w+)\s*_\s*(\d+)/g, '$1_{$2}')  // x _ 0 → x_{0}

    // Fix fraction patterns
    .replace(/(\d+)\s*\/\s*(\d+)/g, '\\frac{$1}{$2}')  // 1 / 2 → \frac{1}{2}

    // Fix square root patterns
    .replace(/sqrt\s*\(\s*([^)]+)\s*\)/g, '\\sqrt{$1}')  // sqrt(x) → \sqrt{x}
    .replace(/√\s*\(\s*([^)]+)\s*\)/g, '\\sqrt{$1}')  // √(x) → \sqrt{x}
    .replace(/√([a-zA-Z0-9]+)/g, '\\sqrt{$1}')  // √x → \sqrt{x}

    // Fix power notation
    .replace(/\^(\d+)/g, '^{$1}')  // ^2 → ^{2}
    .replace(/\*\*(\d+)/g, '^{$1}')  // **2 → ^{2}

    // Fix common function names
    .replace(/\bsin\b/g, '\\sin')
    .replace(/\bcos\b/g, '\\cos')
    .replace(/\btan\b/g, '\\tan')
    .replace(/\bln\b/g, '\\ln')
    .replace(/\blog\b/g, '\\log')
    .replace(/\blim\b/g, '\\lim')

    // Clean up multiple spaces
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Utility function to check if text contains math expressions
 */
export const containsMath = (text) => {
  if (!text || typeof text !== 'string') return false;
  return /(\$\$[\s\S]*?\$\$|\$[^$\n]*?\$)/.test(text);
};

/**
 * Utility function to extract all math expressions from text
 */
export const extractMathExpressions = (text) => {
  if (!text || typeof text !== 'string') return [];

  const mathRegex = /(\$\$[\s\S]*?\$\$|\$[^$\n]*?\$)/g;
  const expressions = [];
  let match;

  while ((match = mathRegex.exec(text)) !== null) {
    const mathExpression = match[0];
    if (mathExpression.startsWith('$$') && mathExpression.endsWith('$$')) {
      expressions.push({
        type: 'display',
        latex: mathExpression.slice(2, -2).trim(),
        original: mathExpression
      });
    } else if (mathExpression.startsWith('$') && mathExpression.endsWith('$')) {
      expressions.push({
        type: 'inline',
        latex: mathExpression.slice(1, -1).trim(),
        original: mathExpression
      });
    }
  }

  return expressions;
};

/**
 * Utility function to strip math expressions from text
 */
export const stripMath = (text) => {
  if (!text || typeof text !== 'string') return text;
  return text.replace(/(\$\$[\s\S]*?\$\$|\$[^$\n]*?\$)/g, '').trim();
};

export default MathTextRenderer;
