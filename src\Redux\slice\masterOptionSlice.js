import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor; // Adjust this path to match your state structure
  return accessToken;
};

// Thunks
export const createMasterOption = createAsyncThunk(
  'masterOption/createMasterOption',
  async (optionData, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MASTER_OPTION}`,
        optionData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data; // This will be handled in the component, but not stored in Redux
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error creating master option');
    }
  }
);

export const getAllMasterOptions = createAsyncThunk(
  'masterOption/getAllMasterOptions',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MASTER_OPTION}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data; // This will be handled in the component, but not stored in Redux
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching master options');
    }
  }
);

export const getMasterOption = createAsyncThunk(
  'masterOption/getMasterOption',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MASTER_OPTION}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data; // This will be handled in the component, but not stored in Redux
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching master option');
    }
  }
);

export const updateMasterOption = createAsyncThunk(
  'masterOption/updateMasterOption',
  async ({ slug, updatedData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MASTER_OPTION}${slug}/`,
        updatedData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data; // This will be handled in the component, but not stored in Redux
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error updating master option');
    }
  }
);

export const deleteMasterOption = createAsyncThunk(
  'masterOption/deleteMasterOption',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MASTER_OPTION}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return slug; // Return the slug to handle in the component, no need to store it in Redux
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error deleting master option');
    }
  }
);

// Slice
const masterOptionSlice = createSlice({
  name: 'masterOption',
  initialState: {
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(createMasterOption.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createMasterOption.fulfilled, (state) => {
        state.isLoading = false;
        // Do not store the created option in Redux state
      })
      .addCase(createMasterOption.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(getAllMasterOptions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getAllMasterOptions.fulfilled, (state) => {
        state.isLoading = false;
        // Do not store the list of options in Redux state
      })
      .addCase(getAllMasterOptions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(getMasterOption.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getMasterOption.fulfilled, (state) => {
        state.isLoading = false;
        // Do not store the single option in Redux state
      })
      .addCase(getMasterOption.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(updateMasterOption.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateMasterOption.fulfilled, (state) => {
        state.isLoading = false;
        // Do not store the updated option in Redux state
      })
      .addCase(updateMasterOption.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(deleteMasterOption.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteMasterOption.fulfilled, (state) => {
        state.isLoading = false;
        // Do not store the result of the deleted option in Redux state
      })
      .addCase(deleteMasterOption.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default masterOptionSlice.reducer;
