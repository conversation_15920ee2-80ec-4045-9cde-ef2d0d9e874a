import React, { useState, useEffect } from "react";
import { But<PERSON>, Form, Container, Row, Col, Spinner } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { createModule } from "../../redux/slice/moduleSlice"; // Redux action to create a module
import { getTestPatterns } from "../../redux/slice/paperEngineSlice"; // Redux action to fetch test patterns
import { Link, useNavigate, useParams } from "react-router-dom";
import NavigationBar from "../../commonComponents/NavigationBar";
import { toast } from "react-hot-toast";
import AllModules from "../components/AllModules";

const AddModule = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { sectionSlug } = useParams(); // Get section from URL parameter
  const accessToken = useSelector((state) => state.contributor.accessToken);
  const { testPatterns, status: testPatternsStatus } = useSelector(
    (state) => state.paperEngine
  ); // Access test patterns from Redux store

  const [moduleName, setModuleName] = useState("");
  const [moduleDescription, setModuleDescription] = useState("");
  const [testPatternValue, setTestPatternValue] = useState(""); // State to hold selected test pattern ID
  const [moduleAdded, setModuleAdded] = useState(false); // State to track module addition
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!accessToken) {
      toast.error("Please log in as a contributor!");
      const timer = setTimeout(() => {
        navigate("/contributor_login");
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [accessToken, navigate]);

  useEffect(() => {
    if (testPatternsStatus === "idle") {
      dispatch(getTestPatterns()); // Fetch test patterns when the component mounts
    }
  }, [dispatch, testPatternsStatus]);

  if (!accessToken) {
    return (
      <Container
        className="d-flex justify-content-center align-items-center text-success"
        style={{ height: "100vh" }}
      >
        <Spinner animation="border" />
      </Container>
    );
  }

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const moduleData = {
      section: sectionSlug, // Use section from URL parameter
      name: moduleName,
      description: moduleDescription,
      ...(testPatternValue && testPatternValue !== "none" && { test_pattern: testPatternValue }),
    };

    dispatch(createModule({ data: moduleData }))
      .unwrap()
      .then(() => {
        setModuleName("");
        setModuleDescription("");
        setTestPatternValue(""); // Reset test pattern selection
        setModuleAdded(true); // Set moduleAdded to true
        toast.success("Module added successfully!");
        setTimeout(() => setModuleAdded(false), 2000); // Reset moduleAdded after delay
      })
      .catch((error) => {
        console.error("Error creating module:", error);
        toast.error("Failed to create module. Please try again.");
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  return (
    <>
      <NavigationBar />
      <Container>
        <Row>
          <Col xs={12} md={4} lg={4}>
            <Form
              onSubmit={handleSubmit}
              className="border mt-5 p-4 rounded shadow-lg"
            >
              <Form.Group controlId="moduleName" className="mb-3">
                <Form.Label>Module Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter module name"
                  value={moduleName}
                  onChange={(e) => setModuleName(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="moduleDescription" className="mb-3">
                <Form.Label>Module Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={4}
                  placeholder="Enter module description"
                  value={moduleDescription}
                  onChange={(e) => setModuleDescription(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="testPattern" className="mb-3">
                <Form.Label>Test Pattern</Form.Label>
                {testPatternsStatus === "loading" ? (
                  <Spinner animation="border" variant="primary" />
                ) : (
                  <Form.Control
                    as="select"
                    value={testPatternValue}
                    onChange={(e) => setTestPatternValue(e.target.value)}
                  >
                    <option value="" disabled>
                      Select a Test Pattern
                    </option>
                    <option value="none">None</option>
                    {testPatterns.map((pattern) => (
                      <option key={pattern.pattern_id} value={pattern.pattern_id}>
                        {pattern.name} (Version: {pattern.version})
                      </option>
                    ))}
                  </Form.Control>
                )}
              </Form.Group>

              <Button
                variant="success"
                type="submit"
                className="w-100"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Add Module"}
              </Button>
            </Form>

            <div className="d-flex justify-content-center align-items-center">
              <Link to="/contributor_dashboard">
                <Button variant="secondary" className="mt-5 text-center">
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </Col>

          <Col xs={12} md={8} lg={8} className="border-left">
            <AllModules moduleAdded={moduleAdded} />
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default AddModule;
