import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Thunks (Async actions)

export const createSubTopic = createAsyncThunk(
  'subTopic/createSubTopic',
  async ({ slug, subTopicData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CREATE_SUB_TOPIC}`,
        subTopicData
      );
      return { slug, subTopic: response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error creating subtopic');
    }
  }
);

export const getSubTopics = createAsyncThunk(
  'subTopic/getSubTopics',
  async (slug, { rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_SUB_TOPICS}${slug}/`
      );
      return { slug, subTopics: response.data.data };
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching subtopics');
    }
  }
);

export const getAllSubTopics = createAsyncThunk(
  'subTopic/getAllSubTopics',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_ALL_SUB_TOPICS}`
      );
      return { subTopics: response.data.data };
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching all subtopics');
    }
  }
);

export const getSingleSubTopic = createAsyncThunk(
  'subTopic/getSingleSubTopic',
  async ({ slug}, { rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_SUB_TOPIC}${slug}/`
      );
      return { slug, subTopic: response.data.data };
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching single subtopic');
    }
  }
);

export const updateSubTopic = createAsyncThunk(
  'subTopic/updateSubTopic',
  async ({ slug, updatedData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_UPDATE_SUB_TOPIC}${slug}/`,
        updatedData
      );
      return {  subTopic: response.data.data };
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error updating subtopic');
    }
  }
);

export const deleteSubTopic = createAsyncThunk(
  'subTopic/deleteSubTopic',
  async ({ slug }, { rejectWithValue }) => {
    try {
      const response = await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_DELETE_SUB_TOPIC}${slug}/`
      );
      return { subslug };
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error deleting subtopic');
    }
  }
);

// Initial state structure
const initialState = {
  topics: [],
  allSubTopics: [],
  loading: false,
  error: null,
};

// Subtopic Slice with separated reducers and extraReducers
const subTopicSlice = createSlice({
  name: 'subTopic',
  initialState,
  reducers: {
    // You can add your synchronous actions here if needed.
  },
  extraReducers: (builder) => {
    builder
      // Create SubTopic
      .addCase(createSubTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createSubTopic.fulfilled, (state, action) => {
        const { slug, subTopic } = action.payload;
        const topic = state.topics.find(t => t.slug === slug);
        if (topic) {
          topic.subTopics.push(subTopic);
        }
        state.loading = false;
      })
      .addCase(createSubTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get SubTopics
      .addCase(getSubTopics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSubTopics.fulfilled, (state, action) => {
        const { slug, subTopics } = action.payload;
        const topic = state.topics.find(t => t.slug === slug);
        if (topic) {
          topic.subTopics = subTopics;
        }
        state.loading = false;
      })
      .addCase(getSubTopics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get All SubTopics
      .addCase(getAllSubTopics.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getAllSubTopics.fulfilled, (state, action) => {
        state.allSubTopics = action.payload.subTopics;
        state.loading = false;
      })
      .addCase(getAllSubTopics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get Single SubTopic
      .addCase(getSingleSubTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getSingleSubTopic.fulfilled, (state, action) => {
        const { slug, subTopic } = action.payload;
        const topic = state.topics.find(t => t.slug === slug);
        if (topic) {
          const subTopicIndex = topic.subTopics.findIndex(st => st.sub_slug === subTopic.sub_slug);
          if (subTopicIndex !== -1) {
            topic.subTopics[subTopicIndex] = subTopic;
          } else {
            topic.subTopics.push(subTopic);
          }
        }
        state.loading = false;
      })
      .addCase(getSingleSubTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update SubTopic
      .addCase(updateSubTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSubTopic.fulfilled, (state, action) => {
        const { slug, subTopic } = action.payload;
        const topic = state.topics.find(t => t.slug === slug);
        if (topic) {
          const subTopicIndex = topic.subTopics.findIndex(st => st.sub_slug === subTopic.sub_slug);
          if (subTopicIndex !== -1) {
            topic.subTopics[subTopicIndex] = subTopic;
          }
        }
        state.loading = false;
      })
      .addCase(updateSubTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete SubTopic
      .addCase(deleteSubTopic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSubTopic.fulfilled, (state, action) => {
        const { slug, sub_slug } = action.payload;
        const topic = state.topics.find(t => t.slug === slug);
        if (topic) {
          topic.subTopics = topic.subTopics.filter(st => st.sub_slug !== sub_slug);
        }
        state.loading = false;
      })
      .addCase(deleteSubTopic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default subTopicSlice.reducer;
