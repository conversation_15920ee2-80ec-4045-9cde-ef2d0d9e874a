import React from 'react';
import PropTypes from 'prop-types';
import { Card, Row, Col } from 'react-bootstrap';
import <PERSON><PERSON><PERSON> from './PieChart'; // Import the PieChart
import Skeleton from 'react-loading-skeleton';

const SmallCard = ({ title, data, isLoading }) => {
  return (
    <Card className="shadow mb-4 p-2">
      <Card.Body>
        <Card.Title className="text-center">
          {isLoading ? (
            <Skeleton width={120} height={20} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
          ) : (
            title
          )}
        </Card.Title>
        <Row className="align-items-center">
          <Col xs={12} md={5} className="shadow">
            {isLoading ? (
              <>
                <Skeleton width="80%" height={15} className="my-2" baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                <Skeleton width="80%" height={15} className="my-2" baseColor="#e6ffe6" highlightColor="#c4f7c4" />
                <Skeleton width="80%" height={15} className="my-2" baseColor="#e6ffe6" highlightColor="#c4f7c4" />
              </>
            ) : (
              <>
                <div className="my-2">
                  <strong style={{ fontSize: "0.9rem" }}>Approved:</strong> {data?.approved || 0}
                </div>
                <div className="my-2">
                  <strong style={{ fontSize: "0.9rem" }}>Pending:</strong> {data?.pending || 0}
                </div>
                <div className="my-2">
                  <strong style={{ fontSize: "0.9rem" }}>Rejected:</strong> {data?.rejected || 0}
                </div>
              </>
            )}
          </Col>

          {/* Right column for PieChart */}
          <Col xs={12} md={7} className="d-flex justify-content-center align-items-center">
            {isLoading ? (
              <Skeleton circle width={100} height={100} baseColor="#e6ffe6" highlightColor="#c4f7c4" />
            ) : (
              <PieChart data={data} />
            )}
          </Col>
        </Row>
      </Card.Body>
    </Card>
  );
};

SmallCard.propTypes = {
  title: PropTypes.string.isRequired,
  data: PropTypes.shape({
    approved: PropTypes.number,
    pending: PropTypes.number,
    rejected: PropTypes.number,
  }).isRequired,
};

export default SmallCard;
