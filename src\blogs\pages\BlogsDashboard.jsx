import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getAllBlogs } from '../../redux/slice/blogSlice';
import { Container, Row, Col, Form, DropdownButton, Dropdown, Pagination } from 'react-bootstrap';
import toast from 'react-hot-toast';
import CreateBlogForm from '../components/CreateBlogForm'; // Import CreateBlogForm
import NavigationBar from '../../commonComponents/NavigationBar';
import BlogCard from '../components/BlogCard';
import { Toaster } from 'react-hot-toast';
import BlogsCardsSkeleton from '../../commonComponents/BlogsCardsSkeleton';

const BlogDashboard = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const accessToken = useSelector((state) => state.contributor.accessToken);
  const [blogs, setBlogs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredBlogs, setFilteredBlogs] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(2);

  // Fetch Blogs when needed
  const fetchBlogs = async () => {
    try {
      const response = await dispatch(getAllBlogs());
      if (response?.payload) {
        setBlogs(response.payload);
      } else {
        toast.error('Error fetching blogs');
      }
    } catch (error) {
      toast.error('Error fetching blogs');
    }
  };

  useEffect(() => {
    if (!accessToken) {
      toast.error('Please log in as a contributor!');
      const timer = setTimeout(() => {
        navigate('/contributor_login');
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [accessToken, navigate]);

  useEffect(() => {
    fetchBlogs();
  }, [dispatch]);

  useEffect(() => {
    if (searchTerm) {
      const filtered = blogs.filter(blog =>
        blog.title.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredBlogs(filtered);
    } else {
      setFilteredBlogs(blogs);
    }
  }, [blogs, searchTerm]);

  const indexOfLastBlog = currentPage * itemsPerPage;
  const indexOfFirstBlog = indexOfLastBlog - itemsPerPage;
  const currentBlogs = filteredBlogs.slice(indexOfFirstBlog, indexOfLastBlog);

  const handlePagination = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleDropdownChange = (number) => {
    setItemsPerPage(number);
    setCurrentPage(1);
  };

  const totalPages = Math.ceil(filteredBlogs.length / itemsPerPage);

  return (
    <>
      <NavigationBar />
      <Container>
        <Row className="mb-4">
          <Col xs={12} md={8} lg={8}>
            {/* Pass fetchBlogs function to CreateBlogForm */}
            <CreateBlogForm fetchBlogs={fetchBlogs} />
          </Col>

          <Col xs={12} md={4} lg={4}>
            <h2 className="text-center text-success my-3" style={{ fontSize: "1.5rem" }}>
              Blogs Dashboard, <small className="h6"> See live updated here. </small>
            </h2>

            <div className="d-flex flex-column flex-md-row justify-content-center mb-3">
              <Form.Control
                type="text"
                placeholder="Search for blogs"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{ width: '100%', maxWidth: '500px', marginBottom: '10px' }}
              />
              <DropdownButton
                id="dropdown-basic-button"
                title={`Blogs per page: ${itemsPerPage}`}
                variant="success"
                onSelect={handleDropdownChange}
                className="mx-1"
              >
                {[1, 2, 3, 6, 9, 18, 60, 99].map((number) => (
                  <Dropdown.Item key={number} eventKey={number}>
                    {number}
                  </Dropdown.Item>
                ))}
              </DropdownButton>
            </div>

            {currentBlogs.length > 0 ? (
              <Row>
                {currentBlogs.map((blog) => (
                  <Col key={blog.id}>
                    <BlogCard blog={blog} fetchBlogs={fetchBlogs} />
                  </Col>
                ))}
              </Row>
            ) : (
              <BlogsCardsSkeleton number={2} cardCol={12}/>
            )}

            <div className="d-flex justify-content-center mt-3">
              <Pagination>
                <Pagination.Prev
                  onClick={() => handlePagination(currentPage - 1)}
                  disabled={currentPage === 1}
                />
                {[...Array(totalPages)].map((_, index) => (
                  <Pagination.Item
                    key={index + 1}
                    active={index + 1 === currentPage}
                    onClick={() => handlePagination(index + 1)}
                  >
                    {index + 1}
                  </Pagination.Item>
                ))}
                <Pagination.Next
                  onClick={() => handlePagination(currentPage + 1)}
                  disabled={currentPage === totalPages}
                />
              </Pagination>
            </div>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default BlogDashboard;
