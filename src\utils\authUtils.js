/**
 * Utility functions for authentication
 */

/**
 * Check if a JWT token is expired
 * @param {string} token - The JWT token to check
 * @returns {boolean} - True if token is valid/not expired, false if expired
 */
export const isTokenValid = (token) => {
  if (!token) return false;
  
  try {
    // Check if it's a JWT token (has 3 parts separated by dots)
    const parts = token.split('.');
    if (parts.length !== 3) {
      return true; // Not a JWT, let server validate
    }
    
    // Decode the payload to check expiration
    const payload = JSON.parse(atob(parts[1]));
    const currentTime = Date.now() / 1000;
    
    // If token has expired
    if (payload.exp && payload.exp < currentTime) {
      return false;
    }
    
    return true;
  } catch (error) {
    // If token can't be decoded, let server validate
    console.warn('Token validation error:', error);
    return true;
  }
};

/**
 * Get token expiration time
 * @param {string} token - The JWT token
 * @returns {Date|null} - Expiration date or null if not a JWT or no expiration
 */
export const getTokenExpiration = (token) => {
  if (!token) return null;
  
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = JSON.parse(atob(parts[1]));
    if (payload.exp) {
      return new Date(payload.exp * 1000);
    }
    
    return null;
  } catch (error) {
    console.warn('Error getting token expiration:', error);
    return null;
  }
};

/**
 * Get time until token expires
 * @param {string} token - The JWT token
 * @returns {number} - Milliseconds until expiration, or -1 if expired/invalid
 */
export const getTimeUntilExpiration = (token) => {
  const expiration = getTokenExpiration(token);
  if (!expiration) return -1;
  
  const now = new Date();
  const timeUntilExpiration = expiration.getTime() - now.getTime();
  
  return timeUntilExpiration > 0 ? timeUntilExpiration : -1;
};

/**
 * Check if user should be warned about token expiration
 * @param {string} token - The JWT token
 * @param {number} warningThresholdMinutes - Minutes before expiration to show warning (default: 5)
 * @returns {boolean} - True if warning should be shown
 */
export const shouldWarnAboutExpiration = (token, warningThresholdMinutes = 5) => {
  const timeUntilExpiration = getTimeUntilExpiration(token);
  if (timeUntilExpiration === -1) return false;
  
  const warningThreshold = warningThresholdMinutes * 60 * 1000; // Convert to milliseconds
  return timeUntilExpiration <= warningThreshold && timeUntilExpiration > 0;
};
