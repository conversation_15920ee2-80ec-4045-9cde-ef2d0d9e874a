const data = 
[
    {
        "question_id": 74,
        "slug": "agsjggggdsgjgsa",
        "subject": [
            "gk"
        ],
        "subject_name": [
            "GK"
        ],
        "topic": [
            "gk-countries"
        ],
        "topic_name": [
            "GK"
        ],
        "sub_topic": [
            "countries-capitals"
        ],
        "sub_topic_name": [
            "GK"
        ],
        "content": "agsjggggdsgjgsa",
        "difficulty": 3,
        "author": 1,
        "current_affairs": 9,
        "is_current_affairs": true,
        "created_at": "2025-02-11T20:09:32.402795+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T20:09:32.402795+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/offer_owqo52z.JPG",
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-wb-ssc-group-d"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 105,
                "question": 74,
                "option_text": "test786786786",
                "slug": "test786786786",
                "is_correct": false,
                "created_at": "2025-02-11T20:09:42.934469+05:30"
            },
            {
                "option_id": 106,
                "question": 74,
                "option_text": "jsdghjghfg",
                "slug": "jsdghjghfg",
                "is_correct": true,
                "created_at": "2025-02-11T20:09:53.767847+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 73,
        "slug": "gjsagfhg",
        "subject": [
            "gk"
        ],
        "subject_name": [
            "GK"
        ],
        "topic": [
            "gk-countries"
        ],
        "topic_name": [
            "GK"
        ],
        "sub_topic": [
            "countries-capitals"
        ],
        "sub_topic_name": [
            "GK"
        ],
        "content": "gjsagfhg",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T20:08:16.708635+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T20:08:16.708635+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/offer_1gq28oI.JPG",
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": 12,
        "is_master": true,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 103,
                "question": 73,
                "option_text": "test9879878",
                "slug": "test9879878",
                "is_correct": true,
                "created_at": "2025-02-11T20:08:28.850761+05:30"
            },
            {
                "option_id": 104,
                "question": 73,
                "option_text": "tetstt",
                "slug": "tetstt",
                "is_correct": false,
                "created_at": "2025-02-11T20:08:34.776267+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 72,
        "slug": "dghjgdhgj",
        "subject": [
            "gk"
        ],
        "subject_name": [
            "GK"
        ],
        "topic": [
            "gk-countries"
        ],
        "topic_name": [
            "GK"
        ],
        "sub_topic": [
            "countries-capitals"
        ],
        "sub_topic_name": [
            "GK"
        ],
        "content": "dghjgdhgj",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T20:05:52.564727+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T20:05:52.564727+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/offer_lScQchS.JPG",
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": 12,
        "is_master": true,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 102,
                "question": 72,
                "option_text": "true 1234545",
                "slug": "true-1234545",
                "is_correct": true,
                "created_at": "2025-02-11T20:07:42.035261+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 71,
        "slug": "testsdjhghgd",
        "subject": [
            "gk"
        ],
        "subject_name": [
            "GK"
        ],
        "topic": [
            "gk-countries"
        ],
        "topic_name": [
            "GK"
        ],
        "sub_topic": [
            "countries-capitals"
        ],
        "sub_topic_name": [
            "GK"
        ],
        "content": "testsdjhghgd",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T19:41:48.657327+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T19:41:48.657327+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/offer_UelCwHL.JPG",
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": 14,
        "is_master_option": true,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 101,
                "question": 71,
                "option_text": "test123",
                "slug": "test123",
                "is_correct": true,
                "created_at": "2025-02-11T19:42:18.921226+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 70,
        "slug": "test-123o",
        "subject": [
            "gk"
        ],
        "subject_name": [
            "GK"
        ],
        "topic": [
            "gk-countries"
        ],
        "topic_name": [
            "GK"
        ],
        "sub_topic": [
            "countries-capitals"
        ],
        "sub_topic_name": [
            "GK"
        ],
        "content": "test 123o",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T19:38:43.960546+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T19:38:43.960546+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/offer_WRoQOBu.JPG",
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": 11,
        "is_master": true,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 100,
                "question": 70,
                "option_text": "true",
                "slug": "True",
                "is_correct": true,
                "created_at": "2025-02-11T19:38:51.593453+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 69,
        "slug": "('demo-question-content1-1', \"A question with the title 'Demo Question Content1?' already exists. Please provide a unique title.\")",
        "subject": [
            "gk",
            "cs"
        ],
        "subject_name": [
            "GK",
            "Computer Science"
        ],
        "topic": [
            "gk-countries",
            "computer-science-number-systems"
        ],
        "topic_name": [
            "GK",
            "Computer Science"
        ],
        "sub_topic": [
            "countries-capitals",
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "GK",
            "Computer Science"
        ],
        "content": "Demo Question Content1?",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T17:06:27.514333+05:30",
        "status": "active",
        "approval_status": "approved",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-12T12:45:54.282738+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [
            {
                "id": 4,
                "year": 2020,
                "month": "January",
                "course": {
                    "id": 11,
                    "name": "WBSSC"
                },
                "exams": {
                    "id": 10,
                    "name": "Group C"
                },
                "status": "active",
                "note": "test",
                "created_at": "2025-02-11T16:53:48.772238Z"
            }
        ],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 68,
        "slug": "murga-couple-test",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "Murga Couple Test",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T13:24:11.488246+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T18:38:54.138674+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/user_joVqzxS.PNG",
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [
            {
                "id": 5,
                "year": 2020,
                "month": "1",
                "course": {
                    "id": 11,
                    "name": "WBSSC"
                },
                "exams": {
                    "id": 11,
                    "name": "Group D"
                },
                "status": "active",
                "note": "test11132231 uyyuyyhuuff",
                "created_at": "2025-02-11T16:57:34.068413Z"
            }
        ],
        "options": [
            {
                "option_id": 99,
                "question": 68,
                "option_text": "This is the correct answer",
                "slug": "this-is-the-correct-answer",
                "is_correct": true,
                "created_at": "2025-02-11T13:24:24.977212+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 65,
        "slug": "test456465",
        "subject": [
            "gk",
            "cs"
        ],
        "subject_name": [
            "GK",
            "Computer Science"
        ],
        "topic": [
            "gk-countries",
            "computer-science-circuit"
        ],
        "topic_name": [
            "GK",
            "Computer Science"
        ],
        "sub_topic": [
            "countries-capitals"
        ],
        "sub_topic_name": [
            "GK",
            "Computer Science"
        ],
        "content": "test456465",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T13:00:21.745986+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T13:00:21.745986+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/user_x47Npd2.PNG",
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-wb-ssc-group-d"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 95,
                "question": 65,
                "option_text": "Test",
                "slug": "test",
                "is_correct": true,
                "created_at": "2025-02-11T13:00:49.457367+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 64,
        "slug": "test-content-123",
        "subject": [
            "cs",
            "lorem-ipsum"
        ],
        "subject_name": [
            "Computer Science",
            "Lorem Ipsum"
        ],
        "topic": [
            "computer-science-number-systems",
            "computer-science-circuit"
        ],
        "topic_name": [
            "Computer Science",
            "Lorem Ipsum"
        ],
        "sub_topic": [
            "number-systems-binary-number-system",
            "number-systems-what-is-loremmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm"
        ],
        "sub_topic_name": [
            "Computer Science",
            "Lorem Ipsum"
        ],
        "content": "Test Content 123",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T12:58:29.589365+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T12:58:29.589365+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 63,
        "slug": "test1239885",
        "subject": [],
        "subject_name": [],
        "topic": [],
        "topic_name": [],
        "sub_topic": [],
        "sub_topic_name": [],
        "content": "test1239885",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T12:55:31.720805+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T12:55:31.720805+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/offer_b1MEKzJ.JPG",
        "language": "english",
        "course": [],
        "subcourse": [],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 62,
        "slug": "test",
        "subject": [],
        "subject_name": [],
        "topic": [],
        "topic_name": [],
        "sub_topic": [],
        "sub_topic_name": [],
        "content": "test",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T12:46:30.731003+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T12:46:30.731003+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/compressed_user_2.PNG",
        "language": "english",
        "course": [],
        "subcourse": [],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [
            {
                "id": 6,
                "year": 2020,
                "month": "January",
                "course": {
                    "id": 11,
                    "name": "WBSSC"
                },
                "exams": {
                    "id": 10,
                    "name": "Group C"
                },
                "status": "active",
                "note": "test7458",
                "created_at": "2025-02-11T17:08:18.558235Z"
            }
        ],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 61,
        "slug": "test-with-data-in-multi-part-form-data",
        "subject": [],
        "subject_name": [],
        "topic": [],
        "topic_name": [],
        "sub_topic": [],
        "sub_topic_name": [],
        "content": "Test with data in multi-part form data",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T12:42:09.254496+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T12:42:09.255496+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/user_WvQlrom.PNG",
        "language": "english",
        "course": [],
        "subcourse": [],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 60,
        "slug": "question-content",
        "subject": [],
        "subject_name": [],
        "topic": [],
        "topic_name": [],
        "sub_topic": [],
        "sub_topic_name": [],
        "content": "Question Content",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T12:19:27.279383+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T12:19:27.279383+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/2024.PNG",
        "language": "english",
        "course": [],
        "subcourse": [],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 93,
                "question": 60,
                "option_text": "This is correct answer",
                "slug": "this-is-correct-answer",
                "is_correct": true,
                "created_at": "2025-02-11T12:19:54.716700+05:30"
            },
            {
                "option_id": 94,
                "question": 60,
                "option_text": "False",
                "slug": "false-11",
                "is_correct": false,
                "created_at": "2025-02-11T12:19:59.017772+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 59,
        "slug": "multi-sub",
        "subject": [
            "gk",
            "cs"
        ],
        "subject_name": [
            "GK",
            "Computer Science"
        ],
        "topic": [
            "gk-countries",
            "computer-science-number-systems"
        ],
        "topic_name": [
            "GK",
            "Computer Science"
        ],
        "sub_topic": [
            "countries-capitals",
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "GK",
            "Computer Science"
        ],
        "content": "multi sub",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T12:13:11.079138+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T12:13:11.079138+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "banking",
            "wbssc"
        ],
        "subcourse": [
            "banking-sbi-po-mock-test-series-2025",
            "wbssc-group-c",
            "wbssc-wb-ssc-group-d"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 91,
                "question": 59,
                "option_text": "false",
                "slug": "false-10",
                "is_correct": false,
                "created_at": "2025-02-11T12:14:07.796503+05:30"
            },
            {
                "option_id": 92,
                "question": 59,
                "option_text": "true",
                "slug": "true-8",
                "is_correct": false,
                "created_at": "2025-02-11T12:14:11.642097+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 58,
        "slug": "test107",
        "subject": [],
        "subject_name": [],
        "topic": [],
        "topic_name": [],
        "sub_topic": [],
        "sub_topic_name": [],
        "content": "test107",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T11:58:22.056488+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T11:58:22.056488+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/sbi_Odx5scL.JPG",
        "language": "english",
        "course": [],
        "subcourse": [],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 89,
                "question": 58,
                "option_text": "True",
                "slug": "true-7",
                "is_correct": true,
                "created_at": "2025-02-11T11:59:29.258794+05:30"
            },
            {
                "option_id": 90,
                "question": 58,
                "option_text": "false",
                "slug": "false-9",
                "is_correct": false,
                "created_at": "2025-02-11T11:59:33.671537+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 57,
        "slug": "this-is-a-test-question15",
        "subject": [],
        "subject_name": [],
        "topic": [],
        "topic_name": [],
        "sub_topic": [],
        "sub_topic_name": [],
        "content": "this is a test question15",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T11:49:09.844655+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T11:49:09.844655+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/sbi_332nxuX.JPG",
        "language": "english",
        "course": [],
        "subcourse": [],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 56,
        "slug": "this-is-a-test-question11",
        "subject": [],
        "subject_name": [],
        "topic": [],
        "topic_name": [],
        "sub_topic": [],
        "sub_topic_name": [],
        "content": "this is a test question11",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T11:42:45.476158+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T11:42:45.476158+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/sbi_rY3GJhv.JPG",
        "language": "english",
        "course": [],
        "subcourse": [],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 55,
        "slug": "this-is-a-test-question10",
        "subject": [
            "gk",
            "cs"
        ],
        "subject_name": [
            "GK",
            "Computer Science"
        ],
        "topic": [
            "gk-countries",
            "computer-science-number-systems"
        ],
        "topic_name": [
            "GK",
            "Computer Science"
        ],
        "sub_topic": [
            "countries-capitals",
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "GK",
            "Computer Science"
        ],
        "content": "this is a test question10",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-11T11:38:04.756061+05:30",
        "status": "active",
        "approval_status": "approved",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-12T13:23:22.190258+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "banking",
            "wbssc"
        ],
        "subcourse": [
            "banking-sbi-po-mock-test-series-2025",
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 53,
        "slug": "normal-question-without-image",
        "subject": [
            "gk",
            "cs"
        ],
        "subject_name": [
            "GK",
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "GK",
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "GK",
            "Computer Science"
        ],
        "content": "Normal question without image",
        "difficulty": 3,
        "author": 1,
        "current_affairs": 9,
        "is_current_affairs": true,
        "created_at": "2025-02-10T19:08:07.047319+05:30",
        "status": "active",
        "approval_status": "approved",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-12T14:26:14.039413+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 52,
        "slug": "blog-question",
        "subject": [
            "gk"
        ],
        "subject_name": [
            "GK"
        ],
        "topic": [
            "gk-countries"
        ],
        "topic_name": [
            "GK"
        ],
        "sub_topic": [
            "countries-capitals"
        ],
        "sub_topic_name": [
            "GK"
        ],
        "content": "Blog / Question",
        "difficulty": 3,
        "author": 1,
        "current_affairs": 9,
        "is_current_affairs": true,
        "created_at": "2025-02-10T18:55:54.525330+05:30",
        "status": "active",
        "approval_status": "approved",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-12T14:25:20.622631+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/user_OaNjQfb.PNG",
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 85,
                "question": 52,
                "option_text": "True",
                "slug": "true-6",
                "is_correct": true,
                "created_at": "2025-02-10T18:56:02.628997+05:30"
            },
            {
                "option_id": 86,
                "question": 52,
                "option_text": "False",
                "slug": "false-8",
                "is_correct": false,
                "created_at": "2025-02-10T18:56:05.563152+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 51,
        "slug": "this-is-a-master-option-with-image",
        "subject": [
            "gk"
        ],
        "subject_name": [
            "GK"
        ],
        "topic": [
            "gk-countries"
        ],
        "topic_name": [
            "GK"
        ],
        "sub_topic": [
            "countries-capitals"
        ],
        "sub_topic_name": [
            "GK"
        ],
        "content": "This is a master Option with image",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-10T17:52:59.644818+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-10T17:52:59.644818+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/offer_banner.JPG",
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": 13,
        "is_master_option": true,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 83,
                "question": 51,
                "option_text": "True",
                "slug": "true-5",
                "is_correct": true,
                "created_at": "2025-02-10T17:53:05.750593+05:30"
            },
            {
                "option_id": 84,
                "question": 51,
                "option_text": "False",
                "slug": "false-7",
                "is_correct": false,
                "created_at": "2025-02-10T17:53:11.749402+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 50,
        "slug": "this-is-a-master-question-with-image",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "This is a master question with image",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-10T17:37:35.069449+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-11T19:11:51.531368+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/2024_cRuUVLD.PNG",
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": 9,
        "is_master": true,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 81,
                "question": 50,
                "option_text": "This is true",
                "slug": "this-is-true",
                "is_correct": true,
                "created_at": "2025-02-10T17:37:44.016964+05:30"
            },
            {
                "option_id": 82,
                "question": 50,
                "option_text": "This option is false",
                "slug": "this-option-is-false",
                "is_correct": false,
                "created_at": "2025-02-10T17:37:51.889108+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 49,
        "slug": "demo-question-content1",
        "subject": [
            "gk",
            "cs"
        ],
        "subject_name": [
            "GK",
            "Computer Science"
        ],
        "topic": [
            "gk-countries",
            "computer-science-number-systems"
        ],
        "topic_name": [
            "GK",
            "Computer Science"
        ],
        "sub_topic": [
            "countries-capitals",
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "GK",
            "Computer Science"
        ],
        "content": "Demo Question Content1?",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-10T17:17:41.290147+05:30",
        "status": "active",
        "approval_status": "rejected",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-13T18:01:53.233954+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [],
        "reason": "Lucky Roaster Couple",
        "reason_document": "/media/reason/user_4wykG9O.PNG"
    },
    {
        "question_id": 48,
        "slug": "this-is-a-test-question-with-image-upload",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "This is a test question with image upload?",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-10T16:10:13.563669+05:30",
        "status": "active",
        "approval_status": "approved",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-12T13:09:00.748439+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/compressed_black_friday.JPG",
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 79,
                "question": 48,
                "option_text": "True",
                "slug": "true-4",
                "is_correct": true,
                "created_at": "2025-02-10T16:10:52.973334+05:30"
            },
            {
                "option_id": 80,
                "question": 48,
                "option_text": "False",
                "slug": "false-6",
                "is_correct": false,
                "created_at": "2025-02-10T16:11:02.963073+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 46,
        "slug": "what-is-the-full-form-of-pubg",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "what is the full form of PUBG ?",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-10T15:53:42.615607+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-12T13:13:04.948205+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 76,
                "question": 46,
                "option_text": "Players Unknown Battle Ground",
                "slug": "players-unknown-battle-ground",
                "is_correct": true,
                "created_at": "2025-02-10T15:54:07.312277+05:30"
            },
            {
                "option_id": 77,
                "question": 46,
                "option_text": "World War Heroes",
                "slug": "world-war-heroes",
                "is_correct": false,
                "created_at": "2025-02-10T15:56:28.877627+05:30"
            },
            {
                "option_id": 78,
                "question": 46,
                "option_text": "BGMI",
                "slug": "bgmi",
                "is_correct": false,
                "created_at": "2025-02-10T15:56:45.630365+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 45,
        "slug": "demo-question-content",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "Demo Question Content?",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-10T13:55:46.341201+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-12T13:26:05.600138+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 44,
        "slug": "this-is-a-test-question9",
        "subject": [],
        "subject_name": [],
        "topic": [],
        "topic_name": [],
        "sub_topic": [],
        "sub_topic_name": [],
        "content": "this is a test question9",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-10T13:20:35.802160+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-10T13:20:35.802160+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": "/media/questions/user_IHrTcPf.PNG",
        "language": "english",
        "course": [],
        "subcourse": [],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 43,
        "slug": "this-is-a-test-question8",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "this is a test question8",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-10T12:21:15.888129+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-10T12:21:15.888129+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 42,
        "slug": "test-1234",
        "subject": [
            "gk"
        ],
        "subject_name": [
            "GK"
        ],
        "topic": [
            "gk-countries"
        ],
        "topic_name": [
            "GK"
        ],
        "sub_topic": [
            "countries-capitals"
        ],
        "sub_topic_name": [
            "GK"
        ],
        "content": "test 1234",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-10T12:19:33.221085+05:30",
        "status": "active",
        "approval_status": "reject",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-13T13:09:55.996425+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 74,
                "question": 42,
                "option_text": "test 1234",
                "slug": "test-1234",
                "is_correct": true,
                "created_at": "2025-02-10T12:19:44.164699+05:30"
            },
            {
                "option_id": 75,
                "question": 42,
                "option_text": "false",
                "slug": "false-5",
                "is_correct": false,
                "created_at": "2025-02-10T12:19:48.567967+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 41,
        "slug": "another-test-after-adding-option-and-conformation-modal-component",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "Another test after Adding Option and conformation modal component",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-08T18:10:06.285588+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-08T18:10:06.285588+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 71,
                "question": 41,
                "option_text": "This is the correct option",
                "slug": "this-is-the-correct-option",
                "is_correct": true,
                "created_at": "2025-02-08T18:10:32.978929+05:30"
            },
            {
                "option_id": 72,
                "question": 41,
                "option_text": "False Answer",
                "slug": "false-answer-1",
                "is_correct": false,
                "created_at": "2025-02-08T18:10:40.698438+05:30"
            },
            {
                "option_id": 73,
                "question": 41,
                "option_text": "false",
                "slug": "false-4",
                "is_correct": false,
                "created_at": "2025-02-08T18:10:56.529813+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 40,
        "slug": "lorem-ipsum-test",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "Lorem Ipsum test?",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-08T18:04:07.089088+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-08T18:04:07.089088+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 69,
                "question": 40,
                "option_text": "Correct Answer",
                "slug": "correct-answer",
                "is_correct": true,
                "created_at": "2025-02-08T18:04:18.110368+05:30"
            },
            {
                "option_id": 70,
                "question": 40,
                "option_text": "False Answer",
                "slug": "false-answer",
                "is_correct": false,
                "created_at": "2025-02-08T18:04:25.850235+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 39,
        "slug": "ashdfsdfhfdhf",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "ashdfsdfhfdhf",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-08T16:58:11.329752+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-12T12:27:10.267814+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 66,
                "question": 39,
                "option_text": "True123",
                "slug": "true123",
                "is_correct": true,
                "created_at": "2025-02-08T16:58:26.872501+05:30"
            },
            {
                "option_id": 67,
                "question": 39,
                "option_text": "False123",
                "slug": "false123",
                "is_correct": false,
                "created_at": "2025-02-08T16:58:32.596666+05:30"
            },
            {
                "option_id": 68,
                "question": 39,
                "option_text": "False12345",
                "slug": "false12345",
                "is_correct": false,
                "created_at": "2025-02-08T16:58:44.466131+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 38,
        "slug": "test13456",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "test13456",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-08T16:52:58.656847+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-08T16:52:58.656847+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 64,
                "question": 38,
                "option_text": "True",
                "slug": "true-3",
                "is_correct": true,
                "created_at": "2025-02-08T16:53:10.748466+05:30"
            },
            {
                "option_id": 65,
                "question": 38,
                "option_text": "False",
                "slug": "false-3",
                "is_correct": false,
                "created_at": "2025-02-08T16:53:16.238971+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 37,
        "slug": "master-option-questions",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "Master Option Questions ?",
        "difficulty": 3,
        "author": 1,
        "current_affairs": 9,
        "is_current_affairs": true,
        "created_at": "2025-02-06T00:16:11.933026+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-06T00:53:37.190750+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "wbssc"
        ],
        "subcourse": [
            "wbssc-group-c"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": 12,
        "is_master_option": true,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 60,
                "question": 37,
                "option_text": "True",
                "slug": "true-2",
                "is_correct": true,
                "created_at": "2025-02-06T00:16:18.412507+05:30"
            },
            {
                "option_id": 61,
                "question": 37,
                "option_text": "False",
                "slug": "false-1",
                "is_correct": false,
                "created_at": "2025-02-06T00:16:23.088109+05:30"
            },
            {
                "option_id": 62,
                "question": 37,
                "option_text": "False",
                "slug": "false-2",
                "is_correct": false,
                "created_at": "2025-02-06T00:16:26.533941+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 36,
        "slug": "blog-master-question-questions",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "Blog /  Master Question /  Questions ?",
        "difficulty": 3,
        "author": 1,
        "current_affairs": 9,
        "is_current_affairs": true,
        "created_at": "2025-02-05T17:19:17.748492+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-05T19:21:15.373119+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "upsc"
        ],
        "subcourse": [
            "upsc-test-sub-course-2"
        ],
        "master_question": 10,
        "is_master": true,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 58,
                "question": 36,
                "option_text": "True",
                "slug": "true-1",
                "is_correct": false,
                "created_at": "2025-02-05T17:19:29.377559+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 35,
        "slug": "this-is-a-question-content-for-testing-master-question12345",
        "subject": [
            "gk"
        ],
        "subject_name": [
            "GK"
        ],
        "topic": [
            "gk-countries"
        ],
        "topic_name": [
            "GK"
        ],
        "sub_topic": [
            "countries-capitals"
        ],
        "sub_topic_name": [
            "GK"
        ],
        "content": "This is a question content for testing master question",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-05T11:29:01.508116+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-05T13:18:57.794216+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "banking"
        ],
        "subcourse": [
            "banking-sbi-po-mock-test-series-2025"
        ],
        "master_question": 8,
        "is_master": true,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 56,
                "question": 35,
                "option_text": "Test 7",
                "slug": "test-option12345",
                "is_correct": true,
                "created_at": "2025-02-05T11:30:27.232785+05:30"
            },
            {
                "option_id": 57,
                "question": 35,
                "option_text": "Test Option 12345",
                "slug": "test-option-12345",
                "is_correct": false,
                "created_at": "2025-02-05T11:30:54.564564+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 32,
        "slug": "this-is-a-demo-question-for-master-option",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "This is a demo question for master option ?",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-04T19:15:42.369281+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-06T01:11:00.890456+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "banking"
        ],
        "subcourse": [
            "banking-sbi-po-mock-test-series-2025"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": 10,
        "is_master_option": true,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 48,
                "question": 32,
                "option_text": "True 12345",
                "slug": "true-12345",
                "is_correct": true,
                "created_at": "2025-02-04T19:17:06.027502+05:30"
            },
            {
                "option_id": 49,
                "question": 32,
                "option_text": "False 123456",
                "slug": "false-123456",
                "is_correct": false,
                "created_at": "2025-02-04T19:17:12.949631+05:30"
            },
            {
                "option_id": 50,
                "question": 32,
                "option_text": "Test Test",
                "slug": "test-test",
                "is_correct": false,
                "created_at": "2025-02-04T19:17:19.597980+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 30,
        "slug": "this-is-question-content-for-current-affairs",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "This is question content for current affairs ?",
        "difficulty": 3,
        "author": 1,
        "current_affairs": 9,
        "is_current_affairs": true,
        "created_at": "2025-02-04T18:15:16.383229+05:30",
        "status": "active",
        "approval_status": "approved",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-12T13:41:39.545612+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "banking"
        ],
        "subcourse": [
            "banking-sbi-po-mock-test-series-2025"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 45,
                "question": 30,
                "option_text": "correct",
                "slug": "correct",
                "is_correct": true,
                "created_at": "2025-02-04T18:15:28.508318+05:30"
            },
            {
                "option_id": 46,
                "question": 30,
                "option_text": "not Correct",
                "slug": "not-correct",
                "is_correct": false,
                "created_at": "2025-02-04T18:15:36.643228+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 29,
        "slug": "test-question-content",
        "subject": [
            "cs"
        ],
        "subject_name": [
            "Computer Science"
        ],
        "topic": [
            "computer-science-number-systems"
        ],
        "topic_name": [
            "Computer Science"
        ],
        "sub_topic": [
            "number-systems-binary-number-system"
        ],
        "sub_topic_name": [
            "Computer Science"
        ],
        "content": "Test Question Content",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-04T18:05:17.020662+05:30",
        "status": "active",
        "approval_status": "pending",
        "updated_by_custmorcare": null,
        "updated_at": "2025-02-04T18:05:17.020662+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "banking"
        ],
        "subcourse": [
            "banking-sbi-po-mock-test-series-2025"
        ],
        "master_question": 7,
        "is_master": true,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 43,
                "question": 29,
                "option_text": "True",
                "slug": "true",
                "is_correct": true,
                "created_at": "2025-02-04T18:05:23.858476+05:30"
            },
            {
                "option_id": 44,
                "question": 29,
                "option_text": "False",
                "slug": "false",
                "is_correct": false,
                "created_at": "2025-02-04T18:05:28.033523+05:30"
            }
        ],
        "reason": null,
        "reason_document": null
    },
    {
        "question_id": 25,
        "slug": "what-is-the-capital-of-france",
        "subject": [
            "gk"
        ],
        "subject_name": [
            "GK"
        ],
        "topic": [
            "gk-countries"
        ],
        "topic_name": [
            "GK"
        ],
        "sub_topic": [
            "countries-capitals"
        ],
        "sub_topic_name": [
            "GK"
        ],
        "content": "What is the Capital of France Edit ?",
        "difficulty": 3,
        "author": 1,
        "current_affairs": null,
        "is_current_affairs": false,
        "created_at": "2025-02-04T15:46:27.501883+05:30",
        "status": "active",
        "approval_status": "rejected",
        "updated_by_custmorcare": 5,
        "updated_at": "2025-02-13T17:37:49.310819+05:30",
        "average_score": 0.0,
        "times_attempted": 0,
        "attachments": null,
        "language": "english",
        "course": [
            "banking"
        ],
        "subcourse": [
            "banking-sbi-po-mock-test-series-2025"
        ],
        "master_question": null,
        "is_master": false,
        "master_option": null,
        "is_master_option": false,
        "previous_year_questions": [],
        "options": [
            {
                "option_id": 31,
                "question": 25,
                "option_text": "New Delhi",
                "slug": "new-delhi",
                "is_correct": false,
                "created_at": "2025-02-04T15:46:34.813946+05:30"
            },
            {
                "option_id": 32,
                "question": 25,
                "option_text": "New Jersey Edit",
                "slug": "new-jersey",
                "is_correct": false,
                "created_at": "2025-02-04T15:46:40.969658+05:30"
            },
            {
                "option_id": 33,
                "question": 25,
                "option_text": "Paris.",
                "slug": "paris",
                "is_correct": true,
                "created_at": "2025-02-04T15:46:45.760531+05:30"
            }
        ],
        "reason": "bas uhi",
        "reason_document": "/media/reason/user.PNG"
    }
]