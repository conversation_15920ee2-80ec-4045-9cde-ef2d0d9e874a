import axios from 'axios';
import { store } from '../Redux/store';
import { handleTokenExpiration } from '../Redux/slice/contributorSlice';
import toast from 'react-hot-toast';

// Flag to prevent multiple redirects
let isRedirecting = false;

// Create axios interceptor for handling token expiration
const setupAxiosInterceptors = () => {
  // Response interceptor to handle authentication errors
  axios.interceptors.response.use(
    (response) => {
      // If response is successful, return it as is
      return response;
    },
    (error) => {
      // Check if error is due to authentication issues
      if (error.response) {
        const { status, data } = error.response;
        
        // Handle 401 Unauthorized (token expired/invalid)
        if (status === 401) {
          handleAuthError('Your session has expired. Please log in again.');
        }
        
        // Handle 403 Forbidden (insufficient permissions or invalid token)
        else if (status === 403) {
          // Check if it's specifically about token expiration
          const errorMessage = data?.detail || data?.message || '';
          if (errorMessage.toLowerCase().includes('token') || 
              errorMessage.toLowerCase().includes('expired') ||
              errorMessage.toLowerCase().includes('invalid')) {
            handleAuthError('Your session has expired. Please log in again.');
          }
        }
      }
      
      // Return the error for further handling by the calling code
      return Promise.reject(error);
    }
  );
};

// Function to handle authentication errors
const handleAuthError = (message) => {
  // Prevent multiple simultaneous redirects
  if (isRedirecting) {
    return;
  }
  
  isRedirecting = true;
  
  // Clear the Redux state with token expiration action
  store.dispatch(handleTokenExpiration());
  
  // Show error message
  toast.error(message);
  
  // Redirect to login page after a short delay
  setTimeout(() => {
    // Reset the flag
    isRedirecting = false;
    
    // Navigate to login page
    window.location.href = '/contributor_login';
  }, 1500);
};

// Function to manually check token expiration (optional utility)
export const checkTokenExpiration = () => {
  const state = store.getState();
  const { accessToken } = state.contributor;
  
  if (!accessToken) {
    return false; // No token means not authenticated
  }
  
  try {
    // Decode JWT token to check expiration (if you're using JWT)
    const tokenPayload = JSON.parse(atob(accessToken.split('.')[1]));
    const currentTime = Date.now() / 1000;
    
    if (tokenPayload.exp < currentTime) {
      // Token is expired
      handleAuthError('Your session has expired. Please log in again.');
      return false;
    }
    
    return true; // Token is valid
  } catch (error) {
    // If token is not a valid JWT or can't be decoded, treat as invalid
    console.warn('Invalid token format:', error);
    return true; // Let the server decide if it's valid
  }
};

// Function to get current auth token (utility function)
export const getCurrentAuthToken = () => {
  const state = store.getState();
  return state.contributor.accessToken;
};

// Function to check if user is authenticated
export const isAuthenticated = () => {
  const token = getCurrentAuthToken();
  return !!token;
};

export default setupAxiosInterceptors;
