import React, { useState, useEffect } from "react";
import { Modal, Form, Button } from "react-bootstrap";
import RichTextEditor from "./RichTextEditor";
import MathEditor from "./MathEditor";
import MathTextRenderer from "./MathTextRenderer";

const MasterQuestionPassageModal = ({
  show,
  handleClose,
  updatedData,
  handleInputChange,
  handleSubmit,
}) => {
  // Math editor state for title
  const [titleMathContent, setTitleMathContent] = useState("");
  const [showTitleMathEditor, setShowTitleMathEditor] = useState(false);

  // Math editor state for passage content
  const [passageMathContent, setPassageMathContent] = useState("");
  const [showPassageMathEditor, setShowPassageMathEditor] = useState(false);

  // Reset math content when modal closes
  useEffect(() => {
    if (!show) {
      setTitleMathContent("");
      setShowTitleMathEditor(false);
      setPassageMathContent("");
      setShowPassageMathEditor(false);
    }
  }, [show]);

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Question</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmit}>
          <Form.Group controlId="formTitle">
            <RichTextEditor
              label="Title"
              name="title"
              value={updatedData.title}
              onChange={handleInputChange}
              placeholder="Enter title"
              rows={2}
              required
            />
          </Form.Group>

          {/* Math Editor Section for Title */}
          <Form.Group className="mb-3">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <Form.Label>Add Mathematical Expressions to Title (Optional)</Form.Label>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={() => setShowTitleMathEditor(!showTitleMathEditor)}
              >
                {showTitleMathEditor ? 'Hide' : 'Show'} Math Editor
              </Button>
            </div>

            {showTitleMathEditor && (
              <MathEditor
                value={titleMathContent}
                onChange={setTitleMathContent}
                label="Mathematical Expression for Title"
                placeholder="Enter mathematical expressions for title..."
                showPreview={true}
                showRawLatex={false}
                displayMode={true}
                embeddedMode={true}
                textContent={updatedData.title}
                onTextContentChange={(newContent) => handleInputChange({
                  target: { name: 'title', value: newContent }
                })}
                className="mb-3"
              />
            )}

            {/* Preview of title with embedded math */}
            {updatedData.title && (
              <div className="mt-2">
                <Form.Label>Title Preview:</Form.Label>
                <div className="border rounded p-3 bg-light">
                  <MathTextRenderer text={updatedData.title} />
                </div>
              </div>
            )}
          </Form.Group>

          <Form.Group controlId="formPassageContent" className="mt-3">
            <RichTextEditor
              label="Passage Content"
              name="passage_content"
              value={updatedData.passage_content}
              onChange={handleInputChange}
              placeholder="Enter passage content"
              rows={4}
              required
            />
          </Form.Group>

          {/* Math Editor Section for Passage Content */}
          <Form.Group className="mb-3">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <Form.Label>Add Mathematical Expressions to Passage (Optional)</Form.Label>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={() => setShowPassageMathEditor(!showPassageMathEditor)}
              >
                {showPassageMathEditor ? 'Hide' : 'Show'} Math Editor
              </Button>
            </div>

            {showPassageMathEditor && (
              <MathEditor
                value={passageMathContent}
                onChange={setPassageMathContent}
                label="Mathematical Expression for Passage"
                placeholder="Enter mathematical expressions for passage content..."
                showPreview={true}
                showRawLatex={false}
                displayMode={true}
                embeddedMode={true}
                textContent={updatedData.passage_content}
                onTextContentChange={(newContent) => handleInputChange({
                  target: { name: 'passage_content', value: newContent }
                })}
                className="mb-3"
              />
            )}

            {/* Preview of passage content with embedded math */}
            {updatedData.passage_content && (
              <div className="mt-2">
                <Form.Label>Passage Preview:</Form.Label>
                <div className="border rounded p-3 bg-light">
                  <MathTextRenderer text={updatedData.passage_content} />
                </div>
              </div>
            )}
          </Form.Group>

          <Button variant="success" type="submit" className="mt-3">
            Save Changes
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default MasterQuestionPassageModal;
