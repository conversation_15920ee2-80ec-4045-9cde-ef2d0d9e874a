import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor; // Adjust this path to match your state structure
  return accessToken;
};

// Create a module
export const createModule = createAsyncThunk(
  'modules/create',
  async ({ data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MODULE}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to create module');
    }
  }
);

// Get all modules
export const getAllModules = createAsyncThunk(
  'modules/getAll',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MODULE}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch modules');
    }
  }
);

// Get a specific module
export const getModule = createAsyncThunk(
  'modules/get',
  async ({ slug }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MODULE}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch module');
    }
  }
);

// Update a module
export const updateModule = createAsyncThunk(
  'modules/update',
  async ({ slug, data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MODULE}${slug}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to update module');
    }
  }
);

// Delete a module
export const deleteModule = createAsyncThunk(
  'modules/delete',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_MODULE}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return slug; // Return the slug to remove the module from the store
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to delete module');
    }
  }
);

const moduleSlice = createSlice({
  name: 'modules',
  initialState: { loading: false, error: null },
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Create module
      .addCase(createModule.pending, (state) => {
        state.loading = true;
      })
      .addCase(createModule.fulfilled, (state, action) => {
        state.loading = false;
        // Do not store the created module in Redux state
      })
      .addCase(createModule.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get all modules
      .addCase(getAllModules.pending, (state) => {
        state.loading = true;
      })
      .addCase(getAllModules.fulfilled, (state, action) => {
        state.loading = false;
        // Do not store the list of modules in Redux state
      })
      .addCase(getAllModules.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Get a specific module
      .addCase(getModule.pending, (state) => {
        state.loading = true;
      })
      .addCase(getModule.fulfilled, (state, action) => {
        state.loading = false;
        // Do not store the specific module in Redux state
      })
      .addCase(getModule.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Update a module
      .addCase(updateModule.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateModule.fulfilled, (state, action) => {
        state.loading = false;
        // Do not store the updated module in Redux state
      })
      .addCase(updateModule.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
      // Delete a module
      .addCase(deleteModule.pending, (state) => {
        state.loading = true;
      })
      .addCase(deleteModule.fulfilled, (state, action) => {
        state.loading = false;
        // Do not store the deleted module in Redux state
      })
      .addCase(deleteModule.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default moduleSlice.reducer;
