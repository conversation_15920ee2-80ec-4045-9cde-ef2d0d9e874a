import React from 'react';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';

// Register chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

const PieChart = ({ data, size = 150 }) => {
  const chartData = {
    labels: ['Approved', 'Pending', 'Rejected'],
    datasets: [
      {
        data: [data?.approved || 0, data?.pending || 0, data?.rejected || 0],
        backgroundColor: ['#28a745', '#ffc107', '#dc3545'], // Updated color scheme
        borderColor: ['#28a745', '#ffc107', '#dc3545'],
        borderWidth: 1,
      },
    ],
  };

  return (
    <div style={{ width: `${size}px`, height: `${size}px` }}>
      <Pie data={chartData} options={{ responsive: true, plugins: { legend: { position: 'top' } } }} />
    </div>
  );
};

export default Pie<PERSON><PERSON>;
