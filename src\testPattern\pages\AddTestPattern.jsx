import React, { useState, useEffect } from "react";
import { Button, Form, Container, Row, Col } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import {
  createTestPattern,
  getTestPatterns,
} from "../../redux/slice/paperEngineSlice"; // Redux actions
import { Link, useNavigate } from "react-router-dom";
import NavigationBar from "../../commonComponents/NavigationBar"; // NavigationBar
import AllTestPatterns from "../components/ViewAllTestPattern"; // Import AllTestPatterns for live updates
import { toast, Toaster } from "react-hot-toast";
import { getSubjects } from "../../redux/slice/subjectSlice"; // Assuming you have this action to get subjects
import Select from "react-select"; // Import react-select

const AddTestPattern = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [isNormalQuestion, setIsNormalQuestion] = useState(false);
  const [isMasterQuestion, setIsMasterQuestion] = useState(false);
  const [isMasterOption, setIsMasterOption] = useState(false);
  const [difficulty, setDifficulty] = useState(3); // Add state for difficulty
  

  const handleNormalQuestionChange = (e) => {
    setIsNormalQuestion(e.target.checked);
  };

  const handleMasterQuestionChange = (e) => {
    setIsMasterQuestion(e.target.checked);
  };

  const handleMasterOptionChange = (e) => {
    setIsMasterOption(e.target.checked);
  };

  const [name, setName] = useState("");
  const [numberOfSections, setNumberOfSections] = useState(1);
  const [sectionDetails, setSectionDetails] = useState([
    {
      sectionName: "",
      timeLimit: 30,
      numberOfQuestions: 30,
    },
  ]);
  const [negativeMarking, setNegativeMarking] = useState(-0.33);
  const [positiveMark, setPositiveMark] = useState(1);
  const [version, setVersion] = useState("1.0");
  // const [selectedSubjects, setSelectedSubjects] = useState([]);
  const [randomTopic, setRandomTopic] = useState(true);
  const [normalQuestionPercentage, setNormalQuestionPercentage] =
    useState(50.0);
  const [masterQuestionPercentage, setMasterQuestionPercentage] =
    useState(25.0);
  const [masterOptionPercentage, setMasterOptionPercentage] = useState(25.0);
  const [patternAdded, setPatternAdded] = useState(false);

  const { subjects } = useSelector((state) => state.subject); // Extract subjects from Redux store
  const { isLoading, error } = useSelector((state) => state.paperEngine); // Access loading and error from Redux

  // Fetch subjects on component mount
  useEffect(() => {
    dispatch(getSubjects());
  }, [dispatch]);

  const validateForm = () => {
    if (
      !name ||
      sectionDetails.some((section) => section.selectedSubjects.length === 0)
    ) {
      toast.error("Please fill out all the required fields.");
      return false;
    }

    if (negativeMarking >= 0) {
      toast.error("Negative marking should be a negative value.");
      return false;
    }

    if (positiveMark <= 0) {
      toast.error("Positive marking should be a positive value.");
      return false;
    }

    for (const section of sectionDetails) {
      if (
        !section.sectionName ||
        section.timeLimit <= 0 ||
        section.numberOfQuestions <= 0
      ) {
        toast.error("All section fields must be filled out correctly.");
        return false;
      }
    }
    return true;
  };

  const handleNumberOfSectionsChange = (e) => {
    const numSections = e.target.value;
    setNumberOfSections(numSections);

    const sections = [];
    for (let i = 0; i < numSections; i++) {
      sections.push({
        sectionName: "",
        timeLimit: 30,
        numberOfQuestions: 30,
        selectedSubjects: [],
      });
    }
    setSectionDetails(sections);
  };

  const handleSectionChange = (index, field, value) => {
    const updatedSections = [...sectionDetails];
    updatedSections[index][field] = value;
    setSectionDetails(updatedSections);
  };

  const handleSubjectChange = (index, selectedSubjects) => {
    const updatedSections = [...sectionDetails];
    updatedSections[index].selectedSubjects = selectedSubjects;
    setSectionDetails(updatedSections);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Run validation before submitting
    if (!validateForm()) return;

    const newPattern = {
      name,
      version,
      sections: sectionDetails.map((section) => ({
        section_name: section.sectionName,
        subject_ids: section.selectedSubjects.map((subject) => subject.value),
        time_limit: section.timeLimit,
        number_of_questions: Number(section.numberOfQuestions),
      })),
      scoring: {
        positive_mark: positiveMark,
        negative_marking: negativeMarking,
      },
      // random_topic: randomTopic,
      difficulty: difficulty, // Add difficulty to the pattern
      question_distribution: {
        normal_question: {
          percentage: normalQuestionPercentage,
          enabled: isNormalQuestion,
        },
        master_question: {
          percentage: masterQuestionPercentage,
          enabled: isMasterQuestion,
        },
        master_option: {
          percentage: masterOptionPercentage,
          enabled: isMasterOption,
        },
      },
    };

    console.log("New pattern: ", newPattern);

    try {
      const res = await dispatch(createTestPattern(newPattern));
      if(res?.meta?.requestStatus === 'fulfilled'){
        setPatternAdded(true);
        toast.success("Test pattern added successfully!");
        // Reset form inputs after submission
        setName("");
        setNumberOfSections(1);
        setSectionDetails([{
          sectionName: "",
          timeLimit: 30,
          numberOfQuestions: 30,
          selectedSubjects: []
        }]);
        setNegativeMarking(-0.33);
        setPositiveMark(1);
        setVersion("1.0");
        setRandomTopic(true);
        setNormalQuestionPercentage(50.0);
        setIsNormalQuestion(true);
        setMasterQuestionPercentage(25.0);
        setIsMasterQuestion(true);
        setMasterOptionPercentage(25.0);
        setIsMasterOption(true);
      }
      // Trigger pattern refresh after successful submission
      setTimeout(() => {
        setPatternAdded(false); // Reset the flag to false after refresh
        dispatch(getTestPatterns()); // Trigger pattern refresh
      }, 500); // Slight delay before fetching new patterns
    } catch (error) {
      toast.error("Failed to add test pattern.");
    }
  };

  return (
    <>
      <NavigationBar />
      <Container className="my-3">
        <Row>
          {/* Left Section: Add Test Pattern Form */}
          <Col md={5}>
            <Form
              onSubmit={handleSubmit}
              className="border mt-1 p-4 rounded shadow-lg"
            >
              <Form.Group controlId="patternName" className="mb-3">
                <Form.Label>Test Pattern Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter pattern name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="numberOfSections" className="mb-3">
                <Form.Label>Number of Sections</Form.Label>
                <Form.Control
                  type="number"
                  placeholder="Enter number of sections"
                  value={numberOfSections}
                  onChange={handleNumberOfSectionsChange}
                  min={1}
                  required
                />
              </Form.Group>

              {sectionDetails.map((section, index) => (
                <div key={index} className="mb-3">
                  <Row>
                    <Col xs={6} sm={6} md={6}>
                      <Form.Label>Section {index + 1} Name</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Enter section name"
                        value={section.sectionName}
                        onChange={(e) =>
                          handleSectionChange(
                            index,
                            "sectionName",
                            e.target.value
                          )
                        }
                        required
                      />
                    </Col>
                    <Col xs={3} sm={3} md={3}>
                      <Form.Label>Time Li.</Form.Label>
                      <Form.Control
                        type="number"
                        value={section.timeLimit}
                        onChange={(e) =>
                          handleSectionChange(
                            index,
                            "timeLimit",
                            e.target.value
                          )
                        }
                        min={1}
                        required
                      />
                    </Col>
                    <Col xs={3} sm={3} md={3}>
                      <Form.Label>Total Q.</Form.Label>
                      <Form.Control
                        type="number"
                        value={section.numberOfQuestions}
                        onChange={(e) =>
                          handleSectionChange(
                            index,
                            "numberOfQuestions",
                            e.target.value
                          )
                        }
                        min={1}
                        required
                      />
                    </Col>
                  </Row>
                  <Form.Group controlId={`subject-${index}`} className="mt-3">
                    <Form.Label>Section {index + 1} Subjects</Form.Label>
                    <Select
                      isMulti
                      options={subjects.map((subject) => ({
                        value: subject?.subject_id,
                        label: subject?.name,
                      }))}
                      value={section?.selectedSubjects}
                      onChange={(selected) =>
                        handleSubjectChange(index, selected)
                      }
                      required
                    />
                  </Form.Group>
                </div>
              ))}

              {/* Negative Marking, Positive Mark, and Subject */}
              <Row className="mb-3">
                <Col xs={6} sm={6} md={6}>
                  <Form.Group controlId="negativeMarking">
                    <Form.Label>Negative Marking</Form.Label>
                    <Form.Control
                      type="number"
                      step="0.01"
                      value={negativeMarking}
                      onChange={(e) => setNegativeMarking(e.target.value)}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col xs={6} sm={6} md={6}>
                  <Form.Group controlId="positiveMark">
                    <Form.Label>Positive Mark</Form.Label>
                    <Form.Control
                      type="number"
                      value={positiveMark}
                      onChange={(e) => setPositiveMark(e.target.value)}
                      required
                    />
                  </Form.Group>
                </Col>
              </Row>

              {/* Add Difficulty Dropdown */}
              <Form.Group controlId="difficulty" className="mb-3">
                <Form.Label>Difficulty</Form.Label>
                <Form.Control
                  as="select"
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value)}
                  required
                >
                  <option value={1}>1 - Easy</option>
                  <option value={2}>2 - Easy</option>
                  <option value={3}>3 - Easy</option>
                  <option value={4}>4 - Easy</option>
                  <option value={5}>5 - Medium</option>
                  <option value={6}>6 - Medium</option>
                  <option value={7}>7 - Medium</option>
                  <option value={8}>8 - Hard</option>
                  <option value={9}>9 - Hard</option>
                  <option value={10}>10 - Extremely Hard</option>
                </Form.Control>
              </Form.Group>

              {/* <Form.Group controlId="randomTopic" className="my-3">
                <Form.Check
                  type="checkbox"
                  label="Random Topic"
                  checked={randomTopic}
                  onChange={(e) => setRandomTopic(e.target.checked)}
                />
              </Form.Group> */}

              <Row>
                <Col md={6}>
                  <Form.Group controlId="isNormalQuestion">
                    <Form.Check
                      type="checkbox"
                      label="Is Normal Question"
                      checked={isNormalQuestion}
                      onChange={handleNormalQuestionChange}
                    />
                  </Form.Group>
                </Col>

                <Col md={6}>
                  <Form.Group controlId="isMasterQuestion">
                    <Form.Check
                      type="checkbox"
                      label="Is Master Question"
                      checked={isMasterQuestion}
                      onChange={handleMasterQuestionChange}
                    />
                  </Form.Group>
                </Col>

                <Col md={6}>
                  <Form.Group controlId="isMasterOption">
                    <Form.Check
                      type="checkbox"
                      label="Is Master Option"
                      checked={isMasterOption}
                      onChange={handleMasterOptionChange}
                    />
                  </Form.Group>
                </Col>

                {isNormalQuestion && (
                  <Col md={12}>
                    <Form.Group controlId="normalQuestionPercentage">
                      <Form.Label>Normal Question Percentage</Form.Label>
                      <Form.Control
                        type="number"
                        placeholder="Enter Percentage"
                        value={normalQuestionPercentage}
                        onChange={(e) =>
                          setNormalQuestionPercentage(e.target.value)
                        }
                      />
                    </Form.Group>
                  </Col>
                )}

                {isMasterQuestion && (
                  <Col md={12}>
                    <Form.Group controlId="masterQuestionPercentage">
                      <Form.Label>Master Question Percentage</Form.Label>
                      <Form.Control
                        type="number"
                        placeholder="Enter Percentage"
                        value={masterQuestionPercentage}
                        onChange={(e) => setMasterQuestionPercentage(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                )}

                {isMasterOption && (
                  <Col md={12}>
                    <Form.Group controlId="masterOptionPercentage">
                      <Form.Label>Master Option Percentage</Form.Label>
                      <Form.Control
                        type="number"
                        placeholder="Enter Percentage"
                        value={masterOptionPercentage}
                        onChange={(e) => setMasterOptionPercentage(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                )}

                <Col md={12} className="text-center mt-2">
                  <Button variant="primary" type="submit">
                    Add Test Pattern
                  </Button>
                </Col>
              </Row>
            </Form>
          </Col>

          {/* Right Section: All Test Patterns */}
          <Col md={7} >
            <AllTestPatterns /> {/* Live update of test patterns */}
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default AddTestPattern;
