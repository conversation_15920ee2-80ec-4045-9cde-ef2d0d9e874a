import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor; 
  return accessToken;
};

// Create a paper
export const createPaper = createAsyncThunk(
  'papers/create',
  async ({ data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PAPER}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to create paper');
    }
  }
);

// Get all papers
export const getAllPapers = createAsyncThunk(
  'papers/getAll',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PAPER}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch papers');
    }
  }
);

// Get a specific paper
export const getPaper = createAsyncThunk(
  'papers/get',
  async ( paperSlug , { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PAPER}${paperSlug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch paper');
    }
  }
);

// Update a paper
export const updatePaper = createAsyncThunk(
  'papers/update',
  async ({ slug, data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PAPER}${slug}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to update paper');
    }
  }
);

// Delete a paper
export const deletePaper = createAsyncThunk(
  'papers/delete',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_PAPER}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return slug;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to delete paper');
    }
  }
);

const paperSlice = createSlice({
  name: 'papers',
  initialState: { error: null },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(createPaper.rejected, (state, action) => {
        state.error = action.payload;
      })
      .addCase(getAllPapers.rejected, (state, action) => {
        state.error = action.payload;
      })
      .addCase(getPaper.rejected, (state, action) => {
        state.error = action.payload;
      })
      .addCase(updatePaper.rejected, (state, action) => {
        state.error = action.payload;
      })
      .addCase(deletePaper.rejected, (state, action) => {
        state.error = action.payload;
      });
  },
});

export default paperSlice.reducer;
