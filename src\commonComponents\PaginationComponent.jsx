import React from "react";
import ReactPaginate from "react-paginate";
import "bootstrap/dist/css/bootstrap.min.css"; // Ensure Bootstrap is imported

const PaginationComponent = ({ totalPages, currentPage, handlePageChange }) => {
  return (
    <div className="d-flex justify-content-center mt-1 mb-3">
      <ReactPaginate
        previousLabel={"Previous"}
        nextLabel={"Next"}
        breakLabel={"..."}
        pageCount={totalPages}
        marginPagesDisplayed={1} // Show first & last pages
        pageRangeDisplayed={3} // Show 3 pages around the current page
        onPageChange={(data) => handlePageChange(data.selected + 1)}
        containerClassName={"pagination justify-content-center"} 
        pageClassName={"page-item"}
        pageLinkClassName={"page-link"}
        previousClassName={"page-item"}
        previousLinkClassName={"page-link"}
        nextClassName={"page-item"}
        nextLinkClassName={"page-link"}
        breakClassName={"page-item"}
        breakLinkClassName={"page-link"}
        activeClassName={"active"}
      />
    </div>
  );
};

export default PaginationComponent;
