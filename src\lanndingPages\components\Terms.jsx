import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, FaClip<PERSON><PERSON>heck, FaList<PERSON>l, FaUserShield, FaExclamationTriangle, FaRegEdit } from 'react-icons/fa'; // React Icons
import styles from './Terms.module.css'; // Import SCSS module

const Terms = () => {
  return (
    <div className={styles.cardContainer}>
      <motion.div
        className={styles.termsCard}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.7 }}
      >
        <motion.h1
          className={styles.termsHeader}
          initial={{ opacity: 0, y: -100 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          Instructions
        </motion.h1>

        <motion.div
          className={styles.termsContent}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.7 }}
        >
          <p>
            Welcome to our Customer Care and Contributor Dashboards. By accessing and using these platforms, you agree to abide by the following terms and conditions. Please read them carefully:
          </p>

          <motion.h2
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <FaLock /> 1. Confidentiality of User Data
          </motion.h2>
          <p>
            All user data collected through the Customer Care and Contributor Dashboards is strictly confidential. You are responsible for ensuring that any information you access or process is kept secure and is not shared or disclosed without proper authorization.
            Any unauthorized disclosure of user data will result in immediate termination of access and may lead to legal action.
          </p>

          <motion.h2
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <FaClipboardCheck /> 2. Accuracy of Information
          </motion.h2>
          <p>
            All users must provide accurate and truthful information. Falsifying data or providing incorrect information intentionally, whether for personal gain or any other reason, is strictly prohibited. Any attempts to manipulate or mislead the system with false information may result in suspension or termination of access.
          </p>

          <motion.h2
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <FaListUl /> 3. Compliance with Company Policies
          </motion.h2>
          <p>
            You are required to follow all company policies, guidelines, and rules while using the platform. This includes respecting the privacy of all users, maintaining a professional tone, and using the platform only for its intended purposes.
            Any actions that violate company policies will lead to disciplinary action, including but not limited to account suspension, and termination of services.
          </p>

          <motion.h2
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.9 }}
          >
            <FaUserShield /> 4. Respectful Use of the Platform
          </motion.h2>
          <p>
            This platform is intended solely for legitimate customer care and contribution purposes. Using the platform to fulfill personal desires, promote third-party interests, or engage in any unauthorized activity is strictly prohibited.
            Do not use the platform for any illegal activities or to harass, threaten, or engage in any form of inappropriate conduct towards other users.
          </p>

          <motion.h2
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 1.1 }}
          >
            <FaExclamationTriangle /> 5. Responsibility for Account Activity
          </motion.h2>
          <p>
            You are solely responsible for the activities that occur under your account. This includes maintaining the confidentiality of your login credentials and ensuring that no unauthorized access occurs.
            Report any suspicious activity or security breaches to the system administrators immediately.
          </p>

          <motion.h2
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 1.3 }}
          >
            <FaRegEdit /> 6. Termination of Access
          </motion.h2>
          <p>
            We reserve the right to suspend or terminate your access to the platform at any time, with or without notice, if we determine that you have violated any of the terms outlined in this agreement.
            Termination of access may also occur if we believe there is a risk to the integrity or security of the platform or its users.
          </p>

          <motion.h2
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 1.5 }}
          >
            <FaClipboardCheck /> 7. Modifications to the Terms
          </motion.h2>
          <p>
            We reserve the right to modify or update these terms and conditions at any time. Any changes will be communicated to you, and continued use of the platform constitutes acceptance of those changes.
          </p>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.7, delay: 1.7 }}
            className={styles.termsFooter}
          >
            By using the Customer Care and Contributor Dashboards, you acknowledge that you have read, understood, and agree to comply with these terms and conditions.
          </motion.p>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Terms;
