import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {  getAllQuestions,  deleteQuestion,  updateQuestion,} from "../../../redux/slice/questionSlice.js";
import {  deleteOption,  updateOption,} from "../../../redux/slice/optionsSlice.js";
import {  Container,  Form,  ButtonGroup,  Button,  Dropdown,  DropdownButton} from "react-bootstrap";
import Swal from "sweetalert2";
import { toast } from "react-hot-toast";
import { createPreviousYearQuestion } from "../../../redux/slice/previousYearQuestionSlice.js";
import PreviousYearQuestionModal from "../../../commonComponents/PreviousYearQuestionModal.jsx";
import imageCompression from "browser-image-compression";
import PaginationComponent from "../../../commonComponents/PaginationComponent.jsx";
import QuestionListSkeleton from "../../../commonComponents/QuestionListSkeleton.jsx";
import NormalQuestionCard from "../../../commonComponents/NormalQuestionCard.jsx";
import QuestionEditModal from "../../../commonComponents/QuestionEditModal.jsx";
import OptionEditModal from "../../../commonComponents/OptionEditModal.jsx";
import AddOptionModal from "../../../commonComponents/AddOptionModal.jsx";

const ViewCurrentAffailsQuestions = ({  searchTerm,  onSearchTermChange,  optionAddedFlag,}) => {
  const contributorProfileId = useSelector(
    (state) => state.contributor.contributorProfileId || null
  );
  const dispatch = useDispatch();
  const [allQuestions, setAllQuestions] = useState([]);
  const [filteredQuestions, setFilteredQuestions] = useState([]);
  const [itemsPerPage, setItemsPerPage] = useState(2);
  const [currentPage, setCurrentPage] = useState(1);
  const [showQuestionModal, setShowQuestionModal] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  // state for updaing the question
  const [updatedQuestionData, setQuestionUpdatedQuestionData] = useState({
    content: "",
    difficulty: 3,
    author: contributorProfileId,
    status: "active",
    // current_affairs: null,
    // is_current_affairs: false,
    approval_status: "pending",
    average_score: 0.0,
    times_attempted: 0,
    subject: "",
    subject_name: "",
    topic: "",
    topic_name: "",
    sub_topic: "",
    sub_topic_name: "",
    language: "",
    course: [],
    subcourse: [],
    // master_question: null,
    // is_master: false,
    // master_option: null,
    // is_master_option: false,
    options: [],
  });
  const [isLoading, setIsLoading] = useState(false);
  const [filter, setFilter] = useState("all"); // Current filter (all, pending, approved, rejected)

  // States for the previous year modal
  const [showPrevYearModal, setShowPrevYearModal] = useState(false);
  const [prevYearQuestionData, setPrevYearQuestionData] = useState({
    question: "", // Question field set to empty initially
    year: 2020,
    month: "January",
    exams: 1, // Changed to a number
    course: 1, // Default course ID
    status: "active",
    note: "",
  });

  // Add these new state variables at the top with other state declarations
  const [newImage, setNewImage] = useState(null);
  const [imageError, setImageError] = useState("");
  const [imagePreview, setImagePreview] = useState(null);
  const [imageSizeText, setImageSizeText] = useState("");

  const imageInputRef = useRef(null);

  const handleImageChange = (e) => {
    const file = e.target.files[0];

    if (!file) return;

    // setIsCheckingImage(true);
    setImageError("");
    setNewImage(null);
    setImagePreview(null);

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 200 * 1024) {
      console.log("Image is less than 200kb: ", file);

      // Directly set the image if it is small enough
      setNewImage(file);
      const reader = new FileReader();
      reader.onload = () => setImagePreview(reader.result);
      reader.readAsDataURL(file);
      // setIsCheckingImage(false);
    } else {
      const options = {
        maxSizeMB: 0.2,
        maxWidthOrHeight: 300,
        useWebWorker: true,
      };

      try {
        imageCompression(file, options)
          .then((compressedFile) => {
            const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);
            console.log("Compressed file size:", compressedFile.size); // Debugging

            if (compressedFile.size <= 200 * 1024) {
              // Convert Blob to File (required for form data)
              const fileName = "compressed_" + file.name;
              const compressedFileAsFile = new File(
                [compressedFile],
                fileName,
                {
                  type: compressedFile.type,
                }
              );

              console.log("Setting compressed image:", compressedFileAsFile);

              // Set the image as File object
              setNewImage(compressedFileAsFile);

              const reader = new FileReader();
              reader.onload = () => setImagePreview(reader.result);
              reader.readAsDataURL(compressedFileAsFile);

              // Display the image sizes in the UI
              setImageSizeText(
                `Original Size: ${originalSizeKB} KB, Compressed Size: ${compressedSizeKB} KB`
              );
            } else {
              setImageError(
                `Image exceeds 200KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`
              );
            }
          })
          .catch((error) => {
            console.error("Image compression failed:", error);
            setImageError("An error occurred while compressing the image.");
          })
          .finally(() => {
            // setIsCheckingImage(false);
          });
      } catch (error) {
        console.error("Error handling image change:", error);
        setImageError("An error occurred while processing the image.");
        // setIsCheckingImage(false);
      }
    }
  };

  // Function to handle marking a question as a Previous Year Question
  const handleMarkAsPrevYear = (question) => {
    setSelectedQuestion(question);
    setPrevYearQuestionData({
      ...prevYearQuestionData,
      question: question.slug, // Automatically set the question ID
    });
    setShowPrevYearModal(true); // Open the modal to mark as previous year question
  };

  // Function to handle submitting the previous year question form
  const handlePrevYearSubmit = async (e) => {
    e.preventDefault();
    try {
      // Submitting the previous year question data
      await dispatch(createPreviousYearQuestion(prevYearQuestionData));
      setShowPrevYearModal(false); // Close the modal after submission
      toast.success("Previous Year Question added successfully!");
    } catch (error) {
      toast.error("Failed to add previous year question.");
    }
  };

  // Function to fetch questions
  const fetchQuestions = async () => {
    setIsLoading(true);
    try {
      const response = await dispatch(getAllQuestions());
      if (response && response.payload) {
        const questions = response.payload;

        // Filter questions with is_current_affairs === true
        const currentAffairsQuestions = questions.filter(
          (question) => question.is_current_affairs
        );

        // Update state with filtered questions
        setAllQuestions(currentAffairsQuestions);

        // Call filterQuestions with the filtered questions
        filterQuestions(currentAffairsQuestions);
      }
    } catch (error) {
      console.error("Error fetching questions:", error);
      toast.error("Failed to load questions");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch questions when component mounts or when `optionAddedFlag` changes
  useEffect(() => {
    fetchQuestions();
  }, [optionAddedFlag]);

  // Filter questions based on searchTerm
  const filterQuestions = (questions) => {
    const filtered = questions.filter((question) =>
      question.content.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredQuestions(filtered);
  };

  // Refactor to filter questions on searchTerm change
  useEffect(() => {
    filterQuestions(allQuestions);
  }, [searchTerm, allQuestions]);

  // Handle filter button clicks
  const handleFilterClick = (newFilter) => {
    setFilter(newFilter);

    let filteredData = allQuestions;

    if (newFilter === "pending") {
      filteredData = allQuestions.filter(
        (q) => q.approval_status === "pending"
      );
    } else if (newFilter === "approved") {
      filteredData = allQuestions.filter(
        (q) => q.approval_status === "approved"
      );
    } else if (newFilter === "rejected") {
      filteredData = allQuestions.filter(
        (q) => q.approval_status === "rejected"
      );
    }

    setFilteredQuestions(filteredData);
  };

  const paginate = (array, pageSize, pageNumber) => {
    const offset = (pageNumber - 1) * pageSize;
    return array.slice(offset, offset + pageSize);
  };

  const handleDropdownChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when items per page changes
  };

  const paginatedQuestions = paginate(
    filteredQuestions,
    itemsPerPage,
    currentPage
  );

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const totalPages = Math.ceil(filteredQuestions.length / itemsPerPage);

  const handleQuestionInputChange = (e) => {
    const { name, value } = e.target;
    setQuestionUpdatedQuestionData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  // FUNCTION FOR EDIT THE QUESTION

  const handleEditQuestion = (question) => {
    setSelectedQuestion(question);
    setQuestionUpdatedQuestionData({
      content: question.content, // Only the question content is editable
      subject: question.subject, // Pre-set subject, topic, and subtopic
      subject_name: question.subject_name,
      topic: question.topic,
      topic_name: question.topic_name,
      sub_topic: question.sub_topic,
      sub_topic_name: question.sub_topic_name,
    });
    // Set initial image preview if question has attachment
    setImagePreview(
      question.attachments
        ? `${import.meta.env.VITE_BASE_URL}/${question.attachments}`
        : null
    );
    setNewImage(null); // Reset new image state
    console.log("SetNewImage is set to null");
    setShowQuestionModal(true); // Show modal for editing
  };

  //Functin For submitting Questions

  const handleSubmitQuestion = async (e) => {
    e.preventDefault();

    try {
      const payload = {
        content: updatedQuestionData.content, // Only update the content
        subject: selectedQuestion.subject, // Keep the original subject
        subject_name: selectedQuestion.subject_name, // Keep the original subject name
        topic: selectedQuestion.topic, // Keep the original topic
        topic_name: selectedQuestion.topic_name, // Keep the original topic name
        sub_topic: selectedQuestion.sub_topic, // Keep the original sub-topic
        sub_topic_name: selectedQuestion.sub_topic_name, // Keep the original sub-topic name
        author: selectedQuestion.author,
        current_affairs: selectedQuestion.current_affairs,
        is_current_affairs: selectedQuestion.is_current_affairs,
        status: selectedQuestion.status,
        approval_status: selectedQuestion.approval_status,
        average_score: selectedQuestion.average_score,
        times_attempted: selectedQuestion.times_attempted,
        language: selectedQuestion.language,
        course: selectedQuestion.course,
        subcourse: selectedQuestion.subcourse,
      };

      if (newImage) {
        console.log("yes new image, passign attachemnt ", newImage);
        payload.attachments = newImage;
      }

      const actionResult = await dispatch(
        updateQuestion({
          questionSlug: selectedQuestion.slug, // Question ID to update
          data: payload,
        })
      );

      // Check if the action was fulfilled
      if (actionResult.meta.requestStatus === "fulfilled") {
        setShowQuestionModal(false);
        fetchQuestions(); // Refetch questions after updating
        toast.success("Question updated successfully!");
      } else {
        toast.error("Failed to update the question.");
      }
    } catch (error) {
      console.error("Error updating question:", error);
      toast.error("An unexpected error occurred while updating the question.");
    }
  };

  // Function to delete a question

  const handleDeleteQuestion = async (questionSlug) => {
    if (!questionSlug) {
      toast.error("Slug is missing, cannot delete the item.");
      return;
    }

    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        const actionResult = await dispatch(deleteQuestion(questionSlug)); // Pass the correct ID here

        // Check if the action was fulfilled
        if (actionResult.meta.requestStatus === "fulfilled") {
          fetchQuestions(); // Refetch questions after deleting
          Swal.fire("Deleted!", "The question has been deleted.", "success");
        } else {
          // Handle failure case
          const errorMessage =
            actionResult.payload?.error || "Failed to delete the question.";
          toast.error(errorMessage);
          console.error("Error response:", actionResult);
        }
      } catch (error) {
        console.error("Error deleting the question:", error);
        toast.error(
          "An unexpected error occurred while deleting the question."
        );
      }
    }
  };

  // States for option edit modal
  const [showOptionModal, setShowOptionModal] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [updatedOptionData, setUpdatedOptionData] = useState({
    option_text: "",
    is_correct: false,
  });
  const [selectedQuestionId, setSelectedQuestionId] = useState(null); // Initialize the selected question ID state

  // States for add option modal
  const [showAddOptionModal, setShowAddOptionModal] = useState(false);
  const [selectedQuestionForOption, setSelectedQuestionForOption] = useState(null);

  // Function to handle editing an option
  const handleEditQuestionOption = (option, questionId) => {
    setSelectedOption(option); // Set the selected option
    setSelectedQuestionId(questionId); // Set the selected question ID
    setUpdatedOptionData({
      option_text: option.option_text,
      is_correct: option.is_correct,
    });
    setShowOptionModal(true); // Open the option edit modal
  };

  // Function to handle deleting an option
  const handleDeleteQuestionOption = (optionSlug, questionSlug) => {
    console.log("Option ID:", optionSlug); // Log optionSlug
    console.log("Question ID:", questionSlug); // Log questionSlug

    Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const actionResult = await dispatch(
            deleteOption({ questionSlug, optionSlug })
          ); // Pass both questionSlug and optionSlug

          // Check if the action was fulfilled
          if (actionResult.meta.requestStatus === "fulfilled") {
            toast.success("Option deleted successfully!");
            fetchQuestions(); // Refetch questions after deletion
          } else {
            // Handle failure case
            const errorMessage =
              actionResult.payload?.error || "Failed to delete the option.";
            toast.error(errorMessage);
            console.error("Error response:", actionResult);
          }
        } catch (error) {
          console.error("Error deleting option:", error);
          toast.error(
            "An unexpected error occurred while deleting the option."
          );
        }
      }
    });
  };

  // Function to handle submitting the updated option
  const handleSubmitQuestionOption = async (e) => {
    e.preventDefault();

    try {
      const actionResult = await dispatch(
        updateOption({
          questionSlug: selectedQuestionId,
          optionSlug: selectedOption.slug, // Pass the option ID to update
          data: {
            option_text: updatedOptionData.option_text, // Updated option text
            is_correct: updatedOptionData.is_correct, // Updated correctness
          },
        })
      );

      // Check if the action was fulfilled
      if (actionResult.meta.requestStatus === "fulfilled") {
        setShowOptionModal(false); // Close the modal after submission
        fetchQuestions(); // Refetch questions to show updated options
        toast.success("Option updated successfully!");
      } else {
        // Handle failure case
        const errorMessage =
          actionResult.payload?.error || "Failed to update the option.";
        toast.error(errorMessage);
        console.error("Error response:", actionResult);
      }
    } catch (error) {
      console.error("Error updating option", error);
      toast.error("An unexpected error occurred while updating the option.");
    }
  };

  return (
    <Container>
      <h2
        className="text-center text-success mb-3"
        style={{ fontSize: "1.5rem" }}
      >
        Current Affairs Questions
        <small className="h6"> See live updated here. </small>
      </h2>

      <div className="d-flex justify-content-center mb-3">
        <Form.Control
          type="text"
          placeholder="Search by content"
          value={searchTerm}
          onChange={(e) => onSearchTermChange(e.target.value)}
          style={{ width: "60%" }}
        />
        <DropdownButton
          id="dropdown-basic-button"
          title={`Questions per page: ${itemsPerPage}`}
          variant="success"
          onSelect={handleDropdownChange}
        >
          {[2, 5, 10, 25, 50, 100].map((number) => (
            <Dropdown.Item key={number} eventKey={number}>
              {number}
            </Dropdown.Item>
          ))}
        </DropdownButton>
      </div>

      <ButtonGroup className="mb-3">
        <Button
          variant={filter === "all" ? "primary" : "outline-primary"}
          onClick={() => handleFilterClick("all")}
        >
          All
        </Button>
        <Button
          variant={filter === "pending" ? "primary" : "outline-primary"}
          onClick={() => handleFilterClick("pending")}
        >
          Pending
        </Button>
        <Button
          variant={filter === "approved" ? "primary" : "outline-primary"}
          onClick={() => handleFilterClick("approved")}
        >
          Approved
        </Button>
        <Button
          variant={filter === "rejected" ? "primary" : "outline-primary"}
          onClick={() => handleFilterClick("rejected")}
        >
          Rejected
        </Button>
      </ButtonGroup>

      {isLoading ? (
        <QuestionListSkeleton number={3} />
      ) : (
        <div className="d-flex flex-wrap">
          {paginatedQuestions.map((question) => (
            <NormalQuestionCard
              key={question.question_id}
              question={question}
              handleEditQuestionOption={handleEditQuestionOption}
              handleDeleteQuestionOption={handleDeleteQuestionOption}
              handleMarkAsPrevYear={handleMarkAsPrevYear}
              handleEditQuestion={handleEditQuestion}
              handleDeleteQuestion={handleDeleteQuestion}
              handleAddOption={(question) => {
                setSelectedQuestionForOption(question);
                setShowAddOptionModal(true);
              }}
            />
          ))}
          {!paginatedQuestions.length && (
            <p className="text-danger mt-3">No questions found.</p>
          )}
        </div>
      )}

      <div className="d-flex justify-content-center mt-1 mb-3">
        <PaginationComponent
          totalPages={totalPages}
          currentPage={currentPage}
          handlePageChange={handlePageChange}
        />
      </div>

      <PreviousYearQuestionModal
        showPrevYearModal={showPrevYearModal}
        setShowPrevYearModal={setShowPrevYearModal}
        prevYearQuestionData={prevYearQuestionData}
        setPrevYearQuestionData={setPrevYearQuestionData}
        handlePrevYearSubmit={handlePrevYearSubmit}
      />

      {/* Modal for Editing */}
      {showQuestionModal && (
        <QuestionEditModal
          show={showQuestionModal}
          onHide={() => setShowQuestionModal(false)}
          updatedQuestionData={updatedQuestionData}
          handleSubmitQuestion={handleSubmitQuestion}
          handleQuestionInputChange={handleQuestionInputChange}
          handleImageChange={handleImageChange}
          imagePreview={imagePreview}
          imageSizeText={imageSizeText}
          imageError={imageError}
        />
      )}

      {/* Modal for Editing Option */}
      {showOptionModal && (
        <OptionEditModal
          show={showOptionModal}
          handleClose={() => setShowOptionModal(false)}
          updatedOptionData={updatedOptionData}
          setUpdatedOptionData={setUpdatedOptionData}
          handleSubmitOption={handleSubmitQuestionOption}
        />
      )}

      {/* Modal for Adding Option */}
      <AddOptionModal
        show={showAddOptionModal}
        onHide={() => setShowAddOptionModal(false)}
        questionSlug={selectedQuestionForOption?.slug}
        onOptionAdded={() => {
          setShowAddOptionModal(false);
          fetchQuestions(); // Refresh questions to show new option
        }}
      />
    </Container>
  );
};

export default ViewCurrentAffailsQuestions;
