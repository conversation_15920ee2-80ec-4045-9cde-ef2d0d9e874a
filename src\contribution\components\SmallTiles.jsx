import React, { useState } from "react";
import PropTypes from "prop-types";
import { Card, Col, Row } from "react-bootstrap";
import {
  FaQuestionCircle,
  FaTasks,
  FaCheckSquare,
  FaBlog,
  FaHistory,
  FaStar,
} from "react-icons/fa";
import ContributionTileSkeleton from "../../commonComponents/ContributionTileSkeleton";

const SmallTiles = ({
  normalQuestions,
  masterQuestions,
  masterOptions,
  blogs,
  previousQuestions,
  totalPoints,
  onTileClick, // Callback to notify parent about the tile clicked
  isLoading,
}) => {
  const [selectedTile, setSelectedTile] = useState(null);

  const handleTileClick = (tileType) => {
    // Update the selected tile
    setSelectedTile(tileType);
    // Call the onTileClick function passed from the parent, passing the selected tile type
    if (onTileClick) onTileClick(tileType);
  };

  // Tile styles
  const tileStyle = {
    display: "flex",
    borderRadius: "8px",
    color: "#fff",
    height: "100%",
    padding: "5px",
    cursor: "pointer", // To change the cursor to a hand symbol
  };

  return (
    <Row xs={1} sm={2} md={3} lg={3} className="g-2">
      {isLoading ? (
        <ContributionTileSkeleton number={6} cardHeightPercentage={90}/>
      ) : (
        <>
          {/* Normal Questions Tile */}
          <Col>
            <Card
              style={{
                ...tileStyle,
                backgroundColor: "#3366cc",
                border:
                  selectedTile === "questions"
                    ? "2px solid #00ffff"
                    : "2px solid #3366cc", // Border on click
              }}
              onClick={() => handleTileClick("questions")} // Trigger click event for normal questions
            >
              <div className="text-center">
                <FaQuestionCircle
                  size="1.1rem"
                  style={{ marginBottom: "2px" }}
                />
                <Card.Title style={{ fontSize: "0.9rem", marginBottom: "2px" }}>
                  Normal Questions
                </Card.Title>
                <Card.Text
                  style={{ fontSize: "0.875rem", marginBottom: "2px" }}
                >
                  {normalQuestions}
                </Card.Text>
              </div>
            </Card>
          </Col>

          {/* Master Questions Tile */}
          <Col>
            <Card
              style={{
                ...tileStyle,
                backgroundColor: "#80e27e",
                border:
                  selectedTile === "master_questions"
                    ? "2px solid #00ffff"
                    : "2px solid #80e27e", // Border on click
              }}
              onClick={() => handleTileClick("master_questions")} // Trigger click event for master questions
            >
              <div className="text-center">
                <FaTasks size="1.1rem" style={{ marginBottom: "2px" }} />
                <Card.Title style={{ fontSize: "0.9rem", marginBottom: "2px" }}>
                  Master Questions
                </Card.Title>
                <Card.Text
                  style={{ fontSize: "0.875rem", marginBottom: "2px" }}
                >
                  {masterQuestions}
                </Card.Text>
              </div>
            </Card>
          </Col>

          {/* Master Options Tile */}
          <Col>
            <Card
              style={{
                ...tileStyle,
                backgroundColor: "#ff7043",
                border:
                  selectedTile === "master_options"
                    ? "2px solid #00ffff"
                    : "2px solid #ff7043", // Border on click
              }}
              onClick={() => handleTileClick("master_options")} // Trigger click event for master options
            >
              <div className="text-center">
                <FaCheckSquare size="1.1rem" style={{ marginBottom: "2px" }} />
                <Card.Title style={{ fontSize: "0.9rem", marginBottom: "2px" }}>
                  Master Options
                </Card.Title>
                <Card.Text
                  style={{ fontSize: "0.875rem", marginBottom: "2px" }}
                >
                  {masterOptions}
                </Card.Text>
              </div>
            </Card>
          </Col>

          {/* Blogs Tile */}
          <Col>
            <Card
              style={{
                ...tileStyle,
                backgroundColor: "#ffb74d",
                border:
                  selectedTile === "blogs"
                    ? "2px solid #00ffff"
                    : "2px solid #ffb74d", // Border on click
              }}
              onClick={() => handleTileClick("blogs")} // Trigger click event for blogs
            >
              <div className="text-center">
                <FaBlog size="1.1rem" style={{ marginBottom: "2px" }} />
                <Card.Title style={{ fontSize: "0.9rem", marginBottom: "2px" }}>
                  Blogs
                </Card.Title>
                <Card.Text
                  style={{ fontSize: "0.875rem", marginBottom: "2px" }}
                >
                  {blogs}
                </Card.Text>
              </div>
            </Card>
          </Col>

          {/* Previous Questions Tile */}
          <Col>
            <Card
              style={{
                ...tileStyle,
                backgroundColor: "#ab47bc",
                border:
                  selectedTile === "previous_questions"
                    ? "2px solid #00ffff"
                    : "2px solid #ab47bc", // Border on click
              }}
              onClick={() => handleTileClick("previous_questions")} // Trigger click event for previous questions
            >
              <div className="text-center">
                <FaHistory size="1.1rem" style={{ marginBottom: "2px" }} />
                <Card.Title style={{ fontSize: "0.9rem", marginBottom: "2px" }}>
                  Previous Questions
                </Card.Title>
                <Card.Text
                  style={{ fontSize: "0.875rem", marginBottom: "2px" }}
                >
                  {previousQuestions}
                </Card.Text>
              </div>
            </Card>
          </Col>

          {/* Total Points Tile (no border changes) */}
          <Col>
            <Card
              style={{
                display: "flex",
                borderRadius: "8px",
                color: "#fff",
                height: "100%",
                padding: "5px",
                backgroundColor: "#fdd835",
                border: "2px solid #fdd835", // No border for Total Points tile
              }}
            >
              <div className="text-center">
                <FaStar size="1.1rem" style={{ marginBottom: "2px" }} />
                <Card.Title style={{ fontSize: "0.9rem", marginBottom: "2px" }}>
                  Total Points
                </Card.Title>
                <Card.Text
                  style={{ fontSize: "0.875rem", marginBottom: "2px" }}
                >
                  {totalPoints}
                </Card.Text>
              </div>
            </Card>
          </Col>
        </>
      )}
    </Row>
  );
};

SmallTiles.propTypes = {
  normalQuestions: PropTypes.number.isRequired,
  masterQuestions: PropTypes.number.isRequired,
  masterOptions: PropTypes.number.isRequired,
  blogs: PropTypes.number.isRequired,
  previousQuestions: PropTypes.number.isRequired,
  totalPoints: PropTypes.number.isRequired,
  onTileClick: PropTypes.func.isRequired, // Callback function
};

export default SmallTiles;
