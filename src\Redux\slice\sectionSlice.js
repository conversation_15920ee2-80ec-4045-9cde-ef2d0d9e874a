import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor;
  return accessToken;
};

// Create a section
export const createSection = createAsyncThunk(
  'sections/create',
  async ({ data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SECTION}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to create section');
    }
  }
);

// Get all sections
export const getAllSections = createAsyncThunk(
  'sections/getAll',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SECTION}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch sections');
    }
  }
);

// Get a specific section
export const getSection = createAsyncThunk(
  'sections/get',
  async ( sectionSlug , { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SECTION}${sectionSlug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to fetch section');
    }
  }
);

// Update a section
export const updateSection = createAsyncThunk(
  'sections/update',
  async ({ slug, data }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SECTION}${slug}/`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to update section');
    }
  }
);

// Delete a section
export const deleteSection = createAsyncThunk(
  'sections/delete',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_SECTION}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return slug; // Return the slug to remove the section from the store
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to delete section');
    }
  }
);

const sectionSlice = createSlice({
  name: 'sections',
  initialState: { searchTerm: '', loading: false, error: null },
  reducers: {
    setSearchTerm: (state, action) => {
      state.searchTerm = action.payload; // Set search term from input
    },
  },
  extraReducers: (builder) => {
    builder
      // Create section
      .addCase(createSection.pending, (state) => {
        state.loading = true;
      })
      .addCase(createSection.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(createSection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get all sections
      .addCase(getAllSections.pending, (state) => {
        state.loading = true;
      })
      .addCase(getAllSections.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(getAllSections.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get a specific section
      .addCase(getSection.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSection.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(getSection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update a section
      .addCase(updateSection.pending, (state) => {
        state.loading = true;
      })
      .addCase(updateSection.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(updateSection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Delete a section
      .addCase(deleteSection.pending, (state) => {
        state.loading = true;
      })
      .addCase(deleteSection.fulfilled, (state, action) => {
        state.loading = false;
      })
      .addCase(deleteSection.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default sectionSlice.reducer;
