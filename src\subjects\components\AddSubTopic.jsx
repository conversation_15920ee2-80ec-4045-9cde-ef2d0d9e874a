import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { createSubTopic } from "../../redux/slice/subTopicSlice"; // Redux action to create a subtopic
import { getSingleTopic } from "../../redux/slice/topicsSlice"; // Redux action to fetch topic details
import { Container, Row, Col, Button, Form, Card  } from "react-bootstrap";

const AddSubTopic = ({ slug, onSubTopicAdded }) => {
  const dispatch = useDispatch();
  const { topic, status } = useSelector((state) => state.topic); // Access topic details from Redux store

  const [subTopicName, setSubTopicName] = useState("");
  const [subTopicDescription, setSubTopicDescription] = useState("");

  // Fetch the topic details when the component mounts
  useEffect(() => {
    if (status === "idle") {
      dispatch(getSingleTopic({ slug }));
    }
  }, [dispatch, status, slug]);

  const handleAddSubTopic = (event) => {
    event.preventDefault(); // Prevent default form submission
    if (subTopicName && subTopicDescription) {
      dispatch(
        createSubTopic({
          subTopicData: {
            topic: slug,
            name: subTopicName,
            description: subTopicDescription,
          },
        })
      ).then(() => {
        // Trigger the callback to refresh the list
        onSubTopicAdded();

        // Reset the form
        setSubTopicName("");
        setSubTopicDescription("");
      });
    } else {
      alert("Please provide all fields for the subtopic.");
    }
  };

  return (
    <div className="add-subtopic-container">
      <Card className="mt-5 shadow-lg rounded-3">
        <Card.Body>
          <Card.Title className="text-center text-success">Add SubTopic</Card.Title>
          <Form onSubmit={handleAddSubTopic}>
            <Form.Group controlId="subTopicName">
              <Form.Label>SubTopic Name</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter subtopic name"
                value={subTopicName}
                onChange={(e) => setSubTopicName(e.target.value)}
              />
            </Form.Group>

            <Form.Group controlId="subTopicDescription" className="mt-2">
              <Form.Label>SubTopic Description</Form.Label>
              <Form.Control
                type="text"
                placeholder="Enter subtopic description"
                value={subTopicDescription}
                onChange={(e) => setSubTopicDescription(e.target.value)}
              />
            </Form.Group>

            <Button variant="success" className="w-100 mt-3" type="submit">
              Add SubTopic
            </Button>
          </Form>
        </Card.Body>
      </Card>

      <div className="d-flex justify-content-center align-items-center">
          <Link to="/contributor_dashboard">
            <Button variant="secondary" className="mt-5 text-center">
              Back to Dashboard
            </Button>
          </Link>
      </div>
    </div>
  );
};

export default AddSubTopic;
