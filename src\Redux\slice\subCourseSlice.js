import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

// Helper function to get the accessToken from Redux state
const getAuthToken = (getState) => {
  const { accessToken } = getState().contributor;
  return accessToken;
};

// Create Subcourse Thunk
export const createSubCourse = createAsyncThunk(
  'subCourse/createSubCourse',
  async ({ _, subCourseData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.post(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_CREATE_SUB_COURSE}`,
        subCourseData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error creating subcourse');
    }
  }
);

// Get Subcourses Thunk
export const getSubCourses = createAsyncThunk(
  'subCourse/getSubCourses',
  async (_, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_SUB_COURSES}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching subcourses');
    }
  }
);

// Get Single Subcourse Thunk
export const getSubCourse = createAsyncThunk(
  'subCourse/getSubCourse',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.get(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_GET_SUB_COURSE}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error fetching subcourse');
    }
  }
);

// Update Subcourse Thunk
export const updateSubCourse = createAsyncThunk(
  'subCourse/updateSubCourse',
  async ({ slug, updatedData }, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.put(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_UPDATE_SUB_COURSE}${slug}/`,
        updatedData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error updating subcourse');
    }
  }
);

// Delete Subcourse Thunk
export const deleteSubCourse = createAsyncThunk(
  'subCourse/deleteSubCourse',
  async (slug, { getState, rejectWithValue }) => {
    try {
      const token = getAuthToken(getState);
      const response = await axios.delete(
        `${import.meta.env.VITE_BASE_URL}${import.meta.env.VITE_DELETE_SUB_COURSE}${slug}/`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return { slug };
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Error deleting subcourse');
    }
  }
);

const subCourseSlice = createSlice({
  name: 'subCourse',
  initialState: {
    subCourse: null,
    subCourses: [],
    isLoading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(createSubCourse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createSubCourse.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subCourse = action.payload;
      })
      .addCase(createSubCourse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(getSubCourses.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSubCourses.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subCourses = action.payload;
      })
      .addCase(getSubCourses.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(getSubCourse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSubCourse.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subCourse = action.payload;
      })
      .addCase(getSubCourse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(updateSubCourse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateSubCourse.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subCourse = action.payload;
      })
      .addCase(updateSubCourse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })
      .addCase(deleteSubCourse.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteSubCourse.fulfilled, (state, action) => {
        state.isLoading = false;
        state.subCourses = state.subCourses.filter(
          (subCourse) => subCourse.slug !== action.payload.slug
        );
      })
      .addCase(deleteSubCourse.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export default subCourseSlice.reducer;
