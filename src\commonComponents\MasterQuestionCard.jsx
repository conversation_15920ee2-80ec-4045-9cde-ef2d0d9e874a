import { <PERSON>, But<PERSON>, Accordion } from "react-bootstrap";
import { Link } from "react-router-dom";
import {
  FaEdit,
  FaPlus,
  FaTrashAlt,
  FaQuestionCircle,
  FaRegCircle,
  FaInfoCircle,
} from "react-icons/fa";
import {
  IoEllipsisHorizontalCircleSharp,
  IoFilterCircle,
} from "react-icons/io5";
import { useState } from "react";
import QuestionRejectReasonModal from "./QuestionRejectReasonModal";
import MathTextRenderer from "./MathTextRenderer";

const QuestionCard = ({
  question,
  handleEditQuestion,
  handleDeleteQuestion,
  handleEditOption,
  handleDeleteOption,
  handleShowReason,
  handleAddOption, // New prop for adding options to existing questions
}) => {
  return (
    <Card
      key={question.slug}
      className="shadow-sm position-relative"
      style={{
        marginBottom: "1rem",
        backgroundColor:
          question?.approval_status === "approved"
            ? "#e6ffee"
            : question.approval_status === "rejected"
            ? "#ffe6e6"
            : "#ffffff",
      }}
    >
      <Card.Body>
        <div className="question-wrapper">
          {/* Question Header with Actions */}
          <div className="d-flex justify-content-between align-items-start mb-3">
            <Card.Title className="flex-grow-1 mb-0">
              <h6 style={{ fontSize: "0.9rem", color: "#6c757d", margin: 0 }}>
                Subject: {question.subject_name} | Topic: {question.topic_name}{" "}
                | Sub Topic: {question.sub_topic_name}
              </h6>
            </Card.Title>
            <div className="question-actions d-flex gap-2 ms-3">
              <Button
                variant="outline-info"
                size="sm"
                onClick={() => handleAddOption && handleAddOption(question)}
                title="Add Options"
              >
                <FaPlus size={12} />
              </Button>
              <Button
                variant="outline-success"
                size="sm"
                onClick={() => handleEditQuestion(question)}
                title="Edit Question"
              >
                <FaEdit size={12} />
              </Button>
              <Button
                variant="outline-danger"
                size="sm"
                onClick={() => handleDeleteQuestion(question.slug)}
                title="Delete Question"
              >
                <FaTrashAlt size={12} />
              </Button>
            </div>
          </div>

          {/* Question Content */}
          <div className="question-content mb-3">
            <Card.Text
              style={{
                textAlign: "justify",
                fontSize: "1.1rem",
                lineHeight: "1.6",
                margin: 0,
              }}
            >
              <FaQuestionCircle className="me-2 text-primary" />
              <MathTextRenderer text={question.content} />
            </Card.Text>
          </div>

          {/* Question Image */}
          {question.attachments && (
            <div className="question-image mb-4">
              <img
                src={`${import.meta.env.VITE_BASE_URL}/${question.attachments}`}
                alt="Question attachment"
                className="img-fluid rounded-3 shadow-sm"
                style={{
                  maxWidth: "100%",
                  height: "auto",
                  border: "1px solid #e9ecef"
                }}
              />
            </div>
          )}

          {/* Multiple Choice Options */}
          <div className="options-section mb-4">
            {question.options && question.options.length > 0 ? (
              <>
                <h6 className="options-title mb-3" style={{ color: "#495057", fontSize: "1rem" }}>
                  Answer Options:
                </h6>
                <ol className="options-list" style={{ listStyleType: "decimal", padding: "0 1rem" }}>
                  {question.options.map((option) => (
                    <li
                      key={option.option_id}
                      className="option-item mb-3 p-3 rounded"
                      style={{
                        backgroundColor: option.is_correct ? "#f8f9fa" : "#ffffff",
                        border: `2px solid ${option.is_correct ? "#198754" : "#dc3545"}`,
                        borderRadius: "8px",
                        display: "flex",
                        alignItems: "flex-start",
                        gap: "12px",
                      }}
                    >
                      <FaRegCircle
                        style={{
                          marginTop: "4px",
                          flexShrink: 0,
                          color: option.is_correct ? "#198754" : "#dc3545"
                        }}
                      />
                      <div className="option-content" style={{ flex: 1, minWidth: 0 }}>
                        <div className="option-text mb-2">
                          <MathTextRenderer text={option.option_text} />
                        </div>
                        {/* Option Image */}
                        {option.attachments && (
                          <div className="option-image mt-2">
                            <img
                              src={`${import.meta.env.VITE_BASE_URL}/${option.attachments}`}
                              alt="Option attachment"
                              className="img-fluid rounded-3 shadow-sm"
                              style={{
                                maxWidth: "250px",
                                height: "auto",
                                border: "1px solid #e9ecef"
                              }}
                            />
                          </div>
                        )}
                      </div>
                      <div className="option-actions d-flex gap-2" style={{ flexShrink: 0 }}>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => handleEditOption(option, question.slug)}
                          title="Edit Option"
                        >
                          <FaEdit size={12} />
                        </Button>
                        <Button
                          variant="outline-danger"
                          size="sm"
                          onClick={() =>
                            handleDeleteOption(option.slug, question.slug)
                          }
                          title="Delete Option"
                        >
                          <FaTrashAlt size={12} />
                        </Button>
                      </div>
                    </li>
                  ))}
                </ol>
              </>
            ) : (
              <div className="no-options text-center py-4">
                <p className="text-muted mb-3">No options available for this question.</p>
                <Button
                  variant="outline-primary"
                  size="sm"
                  onClick={() => handleAddOption && handleAddOption(question)}
                  className="d-flex align-items-center gap-2 mx-auto"
                >
                  <FaPlus size={12} />
                  Add Options
                </Button>
              </div>
            )}
          </div>

          {/* Explanation Section */}
          {question.explanation && (
            <div className="explanation-section">
              <Accordion className="explanation-accordion">
                <Accordion.Item eventKey="0" className="border-0">
                  <Accordion.Header className="explanation-header">
                    <span style={{ color: "#495057", fontWeight: "600" }}>
                      📝 Explanation
                    </span>
                  </Accordion.Header>
                  <Accordion.Body className="explanation-body p-4" style={{ backgroundColor: "#f8f9fa" }}>
                    <div className="explanation-text mb-3">
                      <MathTextRenderer text={question.explanation} />
                    </div>
                    {/* Explanation Image */}
                    {question.explanation_attachment && (
                      <div className="explanation-image mt-3">
                        <img
                          src={`${import.meta.env.VITE_BASE_URL}/${question.explanation_attachment}`}
                          alt="Explanation attachment"
                          className="img-fluid rounded-3 shadow-sm"
                          style={{
                            maxWidth: "100%",
                            height: "auto",
                            border: "1px solid #e9ecef"
                          }}
                        />
                      </div>
                    )}
                  </Accordion.Body>
                </Accordion.Item>
              </Accordion>
            </div>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

const MasterQuestionCard = ({
  masterQuestion,
  handleEdit,
  handleDelete,
  handleEditQuestion,
  handleDeleteQuestion,
  handleEditOption,
  handleDeleteOption,
  handleAddOption, // New prop for adding options to existing questions
}) => {
  const [showReasonModal, setShowReasonModal] = useState(false);

  const handleShowReason = () => setShowReasonModal(true);
  const handleCloseReason = () => setShowReasonModal(false);
  return (
    <>
      <Card
        key={masterQuestion.slug}
        className="shadow position-relative"
        style={{
          width: "100%",
          marginBottom: "1rem",
          backgroundColor:
            masterQuestion.approval_status === "approved"
              ? "#e6ffee"
              : masterQuestion.approval_status === "rejected"
              ? "#ffe6e6"
              : "#ffffb3",
        }}
      >
        <Card.Body>
          {/* Master Question Title and Passage */}
          <div className="mb-3">
            <Card.Title>
              <div className="d-flex align-items-center justify-content-between">
                <div className="w-70">
                  <h5 style={{ fontSize: "1.2rem" }}>
                    <IoEllipsisHorizontalCircleSharp className="fs-5" />{" "}
                    <MathTextRenderer text={masterQuestion.title} />
                  </h5>
                </div>
                <div className="w-30">
                  <Link to={`/master_question/${masterQuestion.slug}`}>
                    <Button
                      variant="outline-success"
                      className="action-buttons m-1"
                    >
                      <FaPlus size={15} />
                    </Button>
                  </Link>
                  <Button
                    variant="outline-primary"
                    className="action-buttons m-2"
                    onClick={() => handleEdit(masterQuestion)}
                  >
                    <FaEdit size={15} />
                  </Button>
                  <Button
                    variant="outline-danger"
                    className="action-buttons m-1"
                    onClick={() => handleDelete(masterQuestion.slug)}
                  >
                    <FaTrashAlt size={15} />
                  </Button>
                </div>
              </div>
            </Card.Title>
            {/* Add rejection reason link */}
            {masterQuestion?.approval_status === "rejected" && (
              <Button
                variant="link"
                className="text-danger p-0 mb-2"
                onClick={handleShowReason}
              >
                <FaInfoCircle className="me-1" />
                See why it rejected
              </Button>
            )}
            <Card.Text
              style={{
                marginRight: "0.7rem",
                textAlign: "justify",
                fontSize: "1.1rem",
              }}
            >
              <IoFilterCircle className="fs-5" />{" "}
              <MathTextRenderer text={masterQuestion.passage_content} />
            </Card.Text>
          </div>

          {/* Questions Under the Passage */}
          <div>
            {masterQuestion?.questions?.length > 0 ? (
              masterQuestion?.questions?.map((question) => (
                <QuestionCard
                  key={question.slug}
                  question={question}
                  handleEditQuestion={handleEditQuestion}
                  handleDeleteQuestion={handleDeleteQuestion}
                  handleEditOption={handleEditOption}
                  handleDeleteOption={handleDeleteOption}
                  handleAddOption={handleAddOption}
                />
              ))
            ) : (
              <p className="text-muted mt-3">
                No questions available for this passage.
              </p>
            )}
          </div>
        </Card.Body>
      </Card>

      <QuestionRejectReasonModal
        show={showReasonModal}
        onHide={handleCloseReason}
        reason={masterQuestion?.reason}
        reasonDocument={masterQuestion?.reason_document}
      />
    </>
  );
};

export default MasterQuestionCard;
