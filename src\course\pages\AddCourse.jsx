import React, { useState, useRef, useEffect } from "react";
import { Button, Form, Container, Row, Col, Spinner } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { createCourse, getCourses } from "../../redux/slice/courseSlice";
import { Link, useNavigate } from 'react-router-dom';
import AllCourses from "../components/AllCourses";
import NavigationBar from "../../commonComponents/NavigationBar";
import imageCompression from 'browser-image-compression';
import { toast } from 'react-hot-toast';

const AddCourse = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const accessToken = useSelector((state) => state.contributor.accessToken);

  useEffect(() => {
    if (!accessToken) {
      toast.error('Please log in as a contributor!');
      const timer = setTimeout(() => {
        navigate('/contributor_login');
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [accessToken, navigate]);

  if (!accessToken) {
    return (
      <Container className="d-flex justify-content-center align-items-center text-success" style={{ height: '100vh' }}>
        <Spinner animation="border" />
      </Container>
    );
  }

  const [courseName, setCourseName] = useState("");
  const [courseDescription, setCourseDescription] = useState("");
  const [image, setImage] = useState(null);
  const [imageError, setImageError] = useState("");
  const [preview, setPreview] = useState(null);
  const [courseAdded, setCourseAdded] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCheckingImage, setIsCheckingImage] = useState(false);
  const [imageSizeText, setImageSizeText] = useState(""); // State for image size text

  const imageInputRef = useRef(null);

  const handleImageChange = (e) => {
    const file = e.target.files[0];

    if (!file) return;

    setIsCheckingImage(true);
    setImageError("");
    setImage(null);
    setPreview(null);

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 20 * 1024) {
      // Directly set the image if it is small enough
      setImage(file);
      const reader = new FileReader();
      reader.onload = () => setPreview(reader.result);
      reader.readAsDataURL(file);
      setIsCheckingImage(false);
    } else {
      const options = {
        maxSizeMB: 0.02,
        maxWidthOrHeight: 300,
        useWebWorker: true,
      };

      try {
        imageCompression(file, options).then((compressedFile) => {
          const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);
          console.log("Compressed file size:", compressedFile.size); // Debugging

          if (compressedFile.size <= 20 * 1024) {
            // Convert Blob to File (required for form data)
            const fileName = "compressed_" + file.name;
            const compressedFileAsFile = new File([compressedFile], fileName, {
              type: compressedFile.type,
            });

            console.log("Setting compressed image:", compressedFileAsFile);

            // Set the image as File object
            setImage(compressedFileAsFile);

            const reader = new FileReader();
            reader.onload = () => setPreview(reader.result);
            reader.readAsDataURL(compressedFileAsFile);

            // Display the image sizes in the UI
            setImageSizeText(
              `Original Size: ${originalSizeKB} KB, Compressed Size: ${compressedSizeKB} KB`
            );
          } else {
            setImageError(`Image exceeds 20KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`);
          }
        }).catch((error) => {
          console.error("Image compression failed:", error);
          setImageError("An error occurred while compressing the image.");
        }).finally(() => {
          setIsCheckingImage(false);
        });
      } catch (error) {
        console.error("Error handling image change:", error);
        setImageError("An error occurred while processing the image.");
        setIsCheckingImage(false);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!image) {
      setImageError("You should upload course logo too.");
    }

    setIsSubmitting(true);
    const courseData = {
      name: courseName,
      description: courseDescription,
      attachments: image, // The compressed image to be uploaded
    };

    try {
      await dispatch(createCourse(courseData)).unwrap();
      setCourseName("");
      setCourseDescription("");
      setImage(null);
      setPreview(null);
      imageInputRef.current.value = null;
      setCourseAdded(true);
      dispatch(getCourses());
    } catch (error) {
      console.error("Error creating course:", error);
    } finally {
      setIsSubmitting(false);
      setImageError("");
    }
  };


  return (
    <>
      <NavigationBar />
      <Container>
        <Row>
          <Col xs={12} md={4} lg={3}>
            <Form onSubmit={handleSubmit} className="border mt-5 p-4 rounded shadow-lg">
              <Form.Group controlId="courseName" className="mb-3">
                <Form.Label>Course Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter course name"
                  value={courseName}
                  onChange={(e) => setCourseName(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="courseDescription" className="mb-3">
                <Form.Label>Course Description</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={4}
                  placeholder="Enter course description"
                  value={courseDescription}
                  onChange={(e) => setCourseDescription(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="courseImage" className="mb-3">
                {imageSizeText && <p className="text-success">{imageSizeText}</p>}

                {imageError && <p className="text-danger mb-2">{imageError}</p>}
                <Form.Label>Course Image (Under 20 KB)</Form.Label>
                <Form.Control
                  ref={imageInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                />
              </Form.Group>

              {preview && (
                <div className="mb-3">
                  <img
                    src={preview}
                    alt="Preview"
                    style={{ width: "100%", maxHeight: "200px", objectFit: "cover" }}
                  />
                </div>
              )}

              <Button
                variant="success"
                type="submit"
                className="w-100"
                disabled={isSubmitting || isCheckingImage || !!imageError}
              >
                {isSubmitting
                  ? "Submitting..."
                  : isCheckingImage
                  ? "Checking image size..."
                  : "Add Course"}
              </Button>
            </Form>

            <div className="d-flex justify-content-center align-items-center">
              <Link to="/contributor_dashboard">
                <Button variant="secondary" className="mt-5 text-center">
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </Col>

          <Col xs={12} md={8} lg={9} className="border-left">
            <AllCourses courseAdded={courseAdded} />
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default AddCourse;
