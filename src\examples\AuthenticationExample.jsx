import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Alert } from 'react-bootstrap';
import { useContributorAuth, withAuth } from '../hooks/useAuth';

// Example 1: Using the useContributorAuth hook directly
const BasicAuthExample = () => {
  const auth = useContributorAuth();
  
  // This component will automatically redirect if not authenticated
  if (!auth.isAuthenticated) {
    return null; // Hook handles the redirect
  }

  return (
    <Card className="mb-4">
      <Card.Header>
        <h5>Basic Authentication Example</h5>
      </Card.Header>
      <Card.Body>
        <p><strong>User:</strong> {auth.profile?.name || 'Unknown'}</p>
        <p><strong>Role:</strong> <Badge bg="primary">{auth.role}</Badge></p>
        <p><strong>Token:</strong> {auth.accessToken ? '✅ Valid' : '❌ Invalid'}</p>
        
        <Button variant="danger" onClick={auth.logout}>
          Logout
        </Button>
      </Card.Body>
    </Card>
  );
};

// Example 2: Using the withAuth HOC
const HOCAuthExample = ({ auth }) => {
  return (
    <Card className="mb-4">
      <Card.Header>
        <h5>HOC Authentication Example</h5>
      </Card.Header>
      <Card.Body>
        <p>This component is wrapped with the withAuth HOC.</p>
        <p><strong>Authentication Status:</strong> 
          <Badge bg={auth.isAuthenticated ? 'success' : 'danger'} className="ms-2">
            {auth.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
          </Badge>
        </p>
        
        {auth.isAuthenticated && (
          <>
            <p><strong>Profile:</strong> {JSON.stringify(auth.profile, null, 2)}</p>
            <Button variant="outline-danger" onClick={auth.logout}>
              Logout via HOC
            </Button>
          </>
        )}
      </Card.Body>
    </Card>
  );
};

// Wrap the component with authentication
const ProtectedHOCExample = withAuth(HOCAuthExample);

// Example 3: Role-based access control
const RoleBasedExample = () => {
  const auth = useContributorAuth();
  
  if (!auth.isAuthenticated) return null;

  return (
    <Card className="mb-4">
      <Card.Header>
        <h5>Role-Based Access Control Example</h5>
      </Card.Header>
      <Card.Body>
        <p><strong>Your Role:</strong> <Badge bg="info">{auth.role}</Badge></p>
        
        {auth.hasRole('admin') && (
          <Alert variant="success">
            🎉 You have admin access! You can see this special content.
          </Alert>
        )}
        
        {auth.hasRole('contributor') && (
          <Alert variant="info">
            📝 You have contributor access! You can create content.
          </Alert>
        )}
        
        {auth.hasAnyRole(['admin', 'moderator']) && (
          <Alert variant="warning">
            🛡️ You have elevated privileges (admin or moderator).
          </Alert>
        )}
        
        {!auth.hasAnyRole(['admin', 'moderator', 'contributor']) && (
          <Alert variant="secondary">
            👤 You have basic user access.
          </Alert>
        )}
      </Card.Body>
    </Card>
  );
};

// Example 4: Optional authentication (doesn't require login)
const OptionalAuthExample = () => {
  const auth = useAuth({
    requireAuth: false,
    showToast: false
  });

  return (
    <Card className="mb-4">
      <Card.Header>
        <h5>Optional Authentication Example</h5>
      </Card.Header>
      <Card.Body>
        {auth.isAuthenticated ? (
          <div>
            <Alert variant="success">
              Welcome back, {auth.profile?.name}! You're logged in.
            </Alert>
            <Button variant="outline-danger" onClick={auth.logout}>
              Logout
            </Button>
          </div>
        ) : (
          <div>
            <Alert variant="info">
              You're not logged in, but you can still see this content.
            </Alert>
            <p>Some features may be limited without authentication.</p>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

// Main example component that demonstrates all patterns
const AuthenticationExample = () => {
  return (
    <Container className="mt-4">
      <h2 className="mb-4">Authentication System Examples</h2>
      
      <Alert variant="info" className="mb-4">
        <strong>Note:</strong> These examples demonstrate different ways to handle authentication in your components.
        The system automatically handles token expiration and redirects users to the login page when needed.
      </Alert>

      <BasicAuthExample />
      <ProtectedHOCExample />
      <RoleBasedExample />
      <OptionalAuthExample />
      
      <Card>
        <Card.Header>
          <h5>How Token Expiration Works</h5>
        </Card.Header>
        <Card.Body>
          <p>When your token expires:</p>
          <ol>
            <li>The system automatically detects 401/403 responses from API calls</li>
            <li>Your session is cleared from Redux store</li>
            <li>You're redirected to the login page with an error message</li>
            <li>No manual intervention required!</li>
          </ol>
          
          <Alert variant="warning">
            <strong>Test it:</strong> Try making an API call with an expired token, 
            and you'll be automatically redirected to login.
          </Alert>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default AuthenticationExample;
