import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useParams, Link } from "react-router-dom";
import { Container, Row, Col, Card, Button, Form, Pagination, Dropdown, DropdownButton } from "react-bootstrap";
import { deletePaper, updatePaper } from "../../redux/slice/paperSlice";
import { getTier } from "../../redux/slice/tierSlice";
import EditPaperModal from "./EditPaperModal";
import ViewModal from "../../commonComponents/ViewModal";

import { BsPencilSquare, BsTrash } from "react-icons/bs";
import toast, { Toaster } from "react-hot-toast";
import Swal from "sweetalert2";

const AllPapers = ({ paperAdded }) => {
  const dispatch = useDispatch();
  const { tierSlug } = useParams();

  const [papers, setPapers] = useState([]);
  const [tierName, setTierName] = useState("");
  const [subCourseName, SetSubCourseName] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [papersPerPage, setPapersPerPage] = useState(6); // Update to allow dynamic changes
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedPaper, setSelectedPaper] = useState(null);
  const [editData, setEditData] = useState({ name: "", max_marks: "", duration: "" });

  const fetchPapers = async () => {
    try {
      const response = await dispatch(getTier(tierSlug)).unwrap();
      setPapers(response.papers);
      setTierName(response.name)
      SetSubCourseName(response.subcourse_name);
    } catch (error) {
      toast.error("Failed to fetch papers. Please try again.");
    }
  };

  useEffect(() => {
    fetchPapers();
  }, [dispatch, paperAdded, tierSlug]);

  const filteredPapers = papers.filter((paper) =>
    (paper.name && paper.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (paper.description && paper.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );


  const indexOfLastPaper = currentPage * papersPerPage;
  const indexOfFirstPaper = indexOfLastPaper - papersPerPage;
  const currentPapers = filteredPapers.slice(indexOfFirstPaper, indexOfLastPaper);

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to the first page when searching
  };

  const handlePapersPerPageChange = (value) => {
    setPapersPerPage(value);
    setCurrentPage(1); // Reset to the first page when changing the number of papers per page
  };

  // for view modal

  const handleViewPaper = (paper) => {
    setSelectedPaper(paper);
    setShowViewModal(true);
  };

  const handleDeletePaper = async (slug) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        await dispatch(deletePaper(slug));
        fetchPapers();
        toast.success("Paper deleted successfully!");
      } catch (error) {
        toast.error("Failed to delete the paper. Please try again.");
      }
    }
  };

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleOpenEditModal = (paper) => {
    setEditData(paper); // Prefill modal data
    setSelectedPaper(paper); // Set selected paper for editing
    setShowEditModal(true);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setSelectedPaper(null);
    setEditData({ name: "", max_marks: "", duration: "" }); // Reset edit data
  };

  const handleEditChange = (e) => {
    const { name, value } = e.target;
    setEditData({
      ...editData,
      [name]: value, // Update the field dynamically
    });
  };

  const handleEditSubmit = async (updatedData) => {
    if (!selectedPaper) {
      toast.error("No paper selected for editing.");
      return; // Exit early if no paper is selected
    }
    const { slug } = selectedPaper; // Now safe to destructure
    try {
      await dispatch(updatePaper({ slug, data: updatedData })).unwrap();
      toast.success("Paper updated successfully!");
      fetchPapers();
      setShowEditModal(false);
      setPapers((prevPapers) =>
        prevPapers.map((paper) =>
          paper.slug === slug ? { ...paper, ...updatedData } : paper
        )
      );
    } catch (error) {
      toast.error("Failed to update paper. Please try again.");
    }
  };

  return (
    <Container>
      <Row>
        <Col xs={12} lg={12} md={12}>
          <h5 className="text-center text-success my-3 h5"> {subCourseName} / {tierName} / Papers</h5>

          <Form.Group className="mb-4 d-flex align-items-center">
            <Form.Control
              type="text"
              placeholder="Search papers..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="me-2"
            />
            <DropdownButton
              id="dropdown-basic-button"
              title={`${papersPerPage} per page`}
              variant="success"
            >
              {[5, 25, 50, 100, "All"].map((option) => (
                <Dropdown.Item
                  key={option}
                  onClick={() =>
                    handlePapersPerPageChange(option === "All" ? filteredPapers.length : option)
                  }
                >
                  {option} per page
                </Dropdown.Item>
              ))}
            </DropdownButton>
          </Form.Group>

          <Row>
            {currentPapers.length > 0 ? (
              currentPapers.map((paper) => (
                <Col key={paper.slug} xs={12} sm={6} md={6} lg={6}>
                  <Card className="mb-4 shadow-sm rounded-3">
                    <Card.Body>
                      <Card.Title className="text-success text-truncate w-100">{paper.name}</Card.Title>
                      <Card.Text className="text-truncate w-100">
                        {paper.description}
                      </Card.Text>
                      <Card.Text>
                        <span> <strong>Max Marks:</strong> {paper.max_marks} </span>
                      </Card.Text>
                      <Card.Text className="mt-2">
                        <span > <strong>Duration:</strong> {paper.duration} </span>
                      </Card.Text>

                      <div className="mt-2">
                        <strong>Test Pattern: </strong>
                        {paper.test_pattern_details ? (
                          <span>
                            {paper.test_pattern_details.name} (Version: {paper.test_pattern_details.version})
                          </span>
                        ) : (
                          <span> N /A </span>
                        )}
                      </div>

                      <div className="d-flex flex-wrap justify-content-center mt-3">
                        <Link to={`/add_section/${paper.slug}`}>
                          <Button variant="outline-primary" className="m-1 fs-6">
                            Section
                          </Button>
                        </Link>

                        <Button variant="outline-info" className="m-1 fs-6" onClick={() => handleViewPaper(paper)}>
                          View
                        </Button>

                        <Button
                          variant="outline-success"
                          onClick={() => handleOpenEditModal(paper)}
                          className="m-1 fs-6"
                        >
                          <BsPencilSquare/>
                        </Button>

                        <Button
                          variant="outline-danger"
                          onClick={() => handleDeletePaper(paper.slug)}
                          className="m-1 fs-6"
                        >
                          <BsTrash/>
                        </Button>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))
            ) : (
              <div className="col-12 text-center">No papers found</div>
            )}
          </Row>

          <div className="d-flex justify-content-center mt-4">
            <Pagination>
              {Array.from({ length: Math.ceil(filteredPapers.length / papersPerPage) }).map(
                (_, index) => (
                  <Pagination.Item
                    key={index + 1}
                    active={index + 1 === currentPage}
                    onClick={() => handlePageChange(index + 1)}
                  >
                    {index + 1}
                  </Pagination.Item>
                )
              )}
            </Pagination>
          </div>
        </Col>
      </Row>

      <EditPaperModal
        show={showEditModal}
        onHide={handleCloseEditModal}
        editData={editData}
        onEditChange={handleEditChange}
        onSubmit={handleEditSubmit}
      />

      <ViewModal show={showViewModal} onHide={() => setShowViewModal(false)} content={selectedPaper} />

      <Toaster />
    </Container>
  );
};

export default AllPapers;
