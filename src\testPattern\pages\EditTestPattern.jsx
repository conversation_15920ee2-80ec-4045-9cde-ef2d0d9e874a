import React, { useState, useEffect } from "react";
import { But<PERSON>, Form, Container, Row, Col, Spinner } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import Select from "react-select";
import { getSingleTestPattern, editTestPattern } from "../../redux/slice/paperEngineSlice"; // Redux actions
import { getSubjects } from "../../redux/slice/subjectSlice"; // Action to get subjects
import NavigationBar from "../../commonComponents/NavigationBar"; // NavigationBar
import ViewAllTestPatterns from "../components/ViewAllTestPattern"; // Import for side component
import { toast, Toaster } from "react-hot-toast";

const EditsingleTestPattern = () => {
  const { id } = useParams(); // Get the test pattern ID from the URL
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // State variables for the form fields
  const [name, setName] = useState("");
  const [numberOfSections, setNumberOfSections] = useState(1);
  const [sectionDetails, setSectionDetails] = useState([
    {
      sectionName: "",
      timeLimit: 30,
      numberOfQuestions: 30,
      selectedSubjects: [],
    },
  ]);
  const [negativeMarking, setNegativeMarking] = useState(-0.33);
  const [positiveMark, setPositiveMark] = useState(1);
  const [version, setVersion] = useState("1.0");
  const [randomTopic, setRandomTopic] = useState(true);
  const [normalQuestionPercentage, setNormalQuestionPercentage] = useState(50.0);
  const [masterQuestionPercentage, setMasterQuestionPercentage] = useState(25.0);
  const [masterOptionPercentage, setMasterOptionPercentage] = useState(25.0);
  const [isNormalQuestion, setIsNormalQuestion] = useState(false);
  const [isMasterQuestion, setIsMasterQuestion] = useState(false);
  const [isMasterOption, setIsMasterOption] = useState(false);
  const [loadingSubjects, setLoadingSubjects] = useState(true); // Loading state for subjects
  const [difficulty, setDifficulty] = useState(1); // Add state for difficulty

  const { singleTestPattern, isLoading, error } = useSelector(
    (state) => state.paperEngine
  ); // Get data from Redux
  const { subjects } = useSelector((state) => state.subject); // Extract subjects from Redux store

  useEffect(() => {
    dispatch(getSingleTestPattern(id)); // Fetch the test pattern by ID when the component mounts
  }, [id, dispatch]);

  useEffect(() => {
    const fetchSubjects = async () => {
      setLoadingSubjects(true); // Set loading state to true while fetching subjects
      try {
        const response = await dispatch(getSubjects()); // Dispatch getSubjects action
        if (response && response.payload && response.payload.success) {
          const fetchedSubjects = response.payload.data; // Get the subjects data from the response
          // setSubjects(fetchedSubjects); // Store subjects in local state
        }
      } catch (error) {
        console.error('Error fetching subjects:', error);
        toast.error('Failed to load subjects.');
      } finally {
        setLoadingSubjects(false); // Set loading state to false after fetching
      }
    };

    fetchSubjects(); // Fetch subjects when the component mounts
  }, [dispatch]);

  useEffect(() => {
    if (singleTestPattern) {
      // Prefill the form with fetched test pattern data
      setName(singleTestPattern.name);
      setNumberOfSections(singleTestPattern.sections.length);
      setSectionDetails(
        singleTestPattern.sections.map((section) => ({
          sectionName: section.section_name,
          timeLimit: section.time_limit,
          numberOfQuestions: Number(section.number_of_questions),
          selectedSubjects: section.subject_ids.map((subjectId) => {
            const subject = subjects.find((sub) => sub.subject_id === subjectId);
            return { value: subjectId, label: subject?.name };
          }),
        }))
      );
      setNegativeMarking(singleTestPattern.negative_marking);
      setPositiveMark(singleTestPattern.positive_mark);
      setVersion(singleTestPattern.version);
      setRandomTopic(singleTestPattern.random_topic);
      setNormalQuestionPercentage(singleTestPattern.normal_question_percentage);
      setIsNormalQuestion(singleTestPattern.is_normal_question);
      setMasterQuestionPercentage(singleTestPattern.master_question_percentage);
      setIsMasterQuestion(singleTestPattern.is_master_question);
      setMasterOptionPercentage(singleTestPattern.master_option_percentage);
      setIsMasterOption(singleTestPattern.is_master_option);
      setDifficulty(singleTestPattern.difficulty); // Set difficulty
    }
  }, [singleTestPattern, subjects]);

  // Form validation
  const validateForm = () => {
    if (!name || sectionDetails.some((section) => section.selectedSubjects.length === 0)) {
      toast.error("Please fill out all the required fields.");
      return false;
    }

    if (negativeMarking >= 0) {
      toast.error("Negative marking should be a negative value.");
      return false;
    }

    if (positiveMark <= 0) {
      toast.error("Positive marking should be a positive value.");
      return false;
    }

    for (const section of sectionDetails) {
      if (!section.sectionName || section.timeLimit <= 0 || section.numberOfQuestions <= 0) {
        toast.error("All section fields must be filled out correctly.");
        return false;
      }
    }
    return true;
  };

  // Handle section details change
  const handleSectionChange = (index, field, value) => {
    const updatedSections = [...sectionDetails];
    updatedSections[index][field] = field === "numberOfQuestions" ? Number(value) : value;
    setSectionDetails(updatedSections);
  };;

  // Handle number of sections change
  const handleNumberOfSectionsChange = (e) => {
    const newNumberOfSections = parseInt(e.target.value, 10);
    setNumberOfSections(newNumberOfSections);

    if (newNumberOfSections > sectionDetails.length) {
      const newSections = [...sectionDetails];
      for (let i = sectionDetails.length; i < newNumberOfSections; i++) {
        newSections.push({
          sectionName: "",
          timeLimit: 30,
          numberOfQuestions: 30,
          selectedSubjects: [],
        });
      }
      setSectionDetails(newSections);
    } else {
      setSectionDetails(sectionDetails.slice(0, newNumberOfSections));
    }
  };

  // Handle removing a section
  const removeSection = (index) => {
    const updatedSections = sectionDetails.filter((_, i) => i !== index);
    setSectionDetails(updatedSections);
    setNumberOfSections(updatedSections.length); // Update the number of sections
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return; // Don't submit if validation fails

    const updatedPattern = {
      name,
      version,
      sections: sectionDetails.map((section) => ({
        section_name: section.sectionName,
        subject_ids: section.selectedSubjects.map((subject) => subject.value),
        time_limit: section.timeLimit,
        number_of_questions: section.numberOfQuestions,
      })),
      scoring: {
        positive_mark: positiveMark,
        negative_marking: negativeMarking,
      },
      // random_topic: randomTopic,
      difficulty: difficulty, // Add difficulty to the pattern
      question_distribution: {
        normal_question: {
          percentage: normalQuestionPercentage,
          enabled: isNormalQuestion,
        },
        master_question: {
          percentage: masterQuestionPercentage,
          enabled: isMasterQuestion,
        },
        master_option: {
          percentage: masterOptionPercentage,
          enabled: isMasterOption,
        },
      },
    };

    try {
      await dispatch(editTestPattern({ id, updatedData: updatedPattern }));
      toast.success("Test pattern updated successfully!");
      navigate("/test_patterns_dashboard");
    } catch (error) {
      toast.error("Failed to update test pattern.");
    }
  };

  return (
    <>
      <NavigationBar />
      <Container className="my-3">
        <Row>
          {/* Left Section: Edit Test Pattern Form */}
          <Col xs={12} md={6}>
            <Form onSubmit={handleSubmit} className="border mt-1 p-4 rounded shadow-lg">
              <Form.Group controlId="patternName" className="mb-3">
                <Form.Label>Test Pattern Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter pattern name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                />
              </Form.Group>

              <Form.Group controlId="numberOfSections" className="mb-3">
                <Form.Label>Number of Sections</Form.Label>
                <Form.Control
                  type="number"
                  value={numberOfSections}
                  onChange={handleNumberOfSectionsChange}
                  min={1}
                  required
                />
              </Form.Group>

              {sectionDetails.map((section, index) => (
                <div key={index} className="mb-3">
                  <Row>
                    <Col xs={6} sm={6} md={6}>
                      <Form.Label>Section {index + 1} Name</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Enter section name"
                        value={section.sectionName}
                        onChange={(e) =>
                          handleSectionChange(index, "sectionName", e.target.value)
                        }
                        required
                      />
                    </Col>
                    <Col xs={3} sm={3} md={3}>
                      <Form.Label>Time Limit</Form.Label>
                      <Form.Control
                        type="number"
                        value={section.timeLimit}
                        onChange={(e) =>
                          handleSectionChange(index, "timeLimit", e.target.value)
                        }
                        min={1}
                        required
                      />
                    </Col>
                    <Col xs={3} sm={3} md={3}>
                      <Form.Label>Total Questions</Form.Label>
                      <Form.Control
                        type="number"
                        value={section.numberOfQuestions}
                        onChange={(e) =>
                          handleSectionChange(index, "numberOfQuestions", e.target.value)
                        }
                        min={1}
                        required
                      />
                    </Col>
                  </Row>
                  <Form.Group controlId={`subject-${index}`} className="mt-3">
                    <Form.Label>Section {index + 1} Subjects</Form.Label>
                    <Select
                      isMulti
                      options={subjects.map((subject) => ({
                        value: subject?.subject_id,
                        label: subject?.name,
                      }))}
                      value={section?.selectedSubjects}
                      onChange={(selected) =>
                        handleSectionChange(index, "selectedSubjects", selected)
                      }
                      required
                    />
                  </Form.Group>
                </div>
              ))}

              <Row className="mb-3">
                <Col xs={6} sm={6} md={6}>
                  <Form.Group controlId="negativeMarking">
                    <Form.Label>Negative Marking</Form.Label>
                    <Form.Control
                      type="number"
                      step="0.01"
                      value={negativeMarking}
                      onChange={(e) => setNegativeMarking(e.target.value)}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col xs={6} sm={6} md={6}>
                  <Form.Group controlId="positiveMark">
                    <Form.Label>Positive Mark</Form.Label>
                    <Form.Control
                      type="number"
                      value={positiveMark}
                      onChange={(e) => setPositiveMark(e.target.value)}
                      required
                    />
                  </Form.Group>
                </Col>
              </Row>

              {/* Add Difficulty Dropdown */}
              <Form.Group controlId="difficulty" className="mb-3">
                <Form.Label>Difficulty</Form.Label>
                <Form.Control
                  as="select"
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value)}
                  required
                >
                  <option value={1}>1 - Easy</option>
                  <option value={2}>2 - Easy</option>
                  <option value={3}>3 - Easy</option>
                  <option value={4}>4 - Easy</option>
                  <option value={5}>5 - Medium</option>
                  <option value={6}>6 - Medium</option>
                  <option value={7}>7 - Medium</option>
                  <option value={8}>8 - Hard</option>
                  <option value={9}>9 - Hard</option>
                  <option value={10}>10 - Extremely Hard</option>
                </Form.Control>
              </Form.Group>

              {/* <Form.Group controlId="randomTopic" className="my-3">
                <Form.Check
                  type="checkbox"
                  label="Random Topic"
                  checked={randomTopic}
                  onChange={(e) => setRandomTopic(e.target.checked)}
                />
              </Form.Group> */}

              <Row>
                <Col md={6}>
                  <Form.Group controlId="isNormalQuestion">
                    <Form.Check
                      type="checkbox"
                      label="Is Normal Question"
                      checked={isNormalQuestion}
                      onChange={(e) => setIsNormalQuestion(e.target.checked)}
                    />
                  </Form.Group>
                </Col>

                <Col md={6}>
                  <Form.Group controlId="isMasterQuestion">
                    <Form.Check
                      type="checkbox"
                      label="Is Master Question"
                      checked={isMasterQuestion}
                      onChange={(e) => setIsMasterQuestion(e.target.checked)}
                    />
                  </Form.Group>
                </Col>

                <Col md={6}>
                  <Form.Group controlId="isMasterOption">
                    <Form.Check
                      type="checkbox"
                      label="Is Master Option"
                      checked={isMasterOption}
                      onChange={(e) => setIsMasterOption(e.target.checked)}
                    />
                  </Form.Group>
                </Col>

                {isNormalQuestion && (
                  <Col md={12}>
                    <Form.Group controlId="normalQuestionPercentage">
                      <Form.Label>Normal Question Percentage</Form.Label>
                      <Form.Control
                        type="number"
                        placeholder="Enter Percentage"
                        value={normalQuestionPercentage}
                        onChange={(e) =>
                          setNormalQuestionPercentage(e.target.value)
                        }
                      />
                    </Form.Group>
                  </Col>
                )}

                {isMasterQuestion && (
                  <Col md={12}>
                    <Form.Group controlId="masterQuestionPercentage">
                      <Form.Label>Master Question Percentage</Form.Label>
                      <Form.Control
                        type="number"
                        placeholder="Enter Percentage"
                        value={masterQuestionPercentage}
                        onChange={(e) => setMasterQuestionPercentage(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                )}

                {isMasterOption && (
                  <Col md={12}>
                    <Form.Group controlId="masterOptionPercentage">
                      <Form.Label>Master Option Percentage</Form.Label>
                      <Form.Control
                        type="number"
                        placeholder="Enter Percentage"
                        value={masterOptionPercentage}
                        onChange={(e) => setMasterOptionPercentage(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                )}

                <Col md={12} className="text-center mt-2">
                  <Button variant="primary" type="submit" disabled={isLoading}>
                    {isLoading ? "Updating..." : "Update Test Pattern"}
                  </Button>
                </Col>
              </Row>
            </Form>
          </Col>

          {/* Right Section: View All Test Patterns */}
          <Col xs={12} md={6}>
            <ViewAllTestPatterns showButtons={false} />
          </Col>
        </Row>
        <Toaster />
      </Container>
    </>
  );
};

export default EditsingleTestPattern;