import React, { useState, useEffect, useRef } from "react";
import { Card, Form, Button, Row, Col } from "react-bootstrap";
import JoditEditor from "jodit-react";
import RichTextEditor from "../../commonComponents/RichTextEditor";


const EditBlogForm = ({ initialValues, onSubmit, loading }) => {
  const editorRef = useRef(null);
  const [formData, setFormData] = useState(initialValues);
  const [preview, setPreview] = useState(null);
  const [imageError, setImageError] = useState("");

  useEffect(() => {
    setFormData(initialValues);
  }, [initialValues]);

  useEffect(() => {
    if (formData.image && typeof formData.image === "string") {
      console.log("img url in string: ", formData.image);
      setPreview(`${import.meta.env.VITE_BASE_URL}/${formData.image}`);
    } else if (formData.image && formData.image instanceof File) {
      const objectUrl = URL.createObjectURL(formData.image);
      console.log("img url: ", objectUrl);

      setPreview(objectUrl);
      return () => URL.revokeObjectURL(objectUrl);
    } else {
      setPreview(null);
    }
  }, [formData.image]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleJoditChange = (content) => {
    setFormData((prevData) => ({
      ...prevData,
      content,
    }));
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    const file = files[0];

    // Validate image file
    if (file) {
      const allowedTypes = ["image/jpeg", "image/png", "image/gif"];
      const maxSize = 150 * 1024; // 150 KB

      if (!allowedTypes.includes(file.type)) {
        setImageError("Invalid image type. Only JPG, PNG, and GIF are allowed.");
        return;
      }

      if (file.size > maxSize) {
        setImageError("File size exceeds the 150 KB limit.");
        return;
      }

      setImageError("");
      setFormData((prevData) => ({
        ...prevData,
        [name]: file,
      }));
    }
  };

  const handleRemoveImage = () => {
    setFormData((prevData) => ({
      ...prevData,
      image: null,
    }));
    setPreview(null);
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Only include image if it's a File type
    const formDataToSubmit = {
      ...formData,
    };

    // Only include the image if it's a File type
    if (!(formData.image instanceof File)) {
      delete formDataToSubmit.image;
    }

    onSubmit(formDataToSubmit);
    setPreview(null);
  };

  const editorConfig = {
    readonly: false, // Enables editing
    placeholder: "Write your content here...",
    toolbarSticky: false, // Toolbar won't stick to the top
    buttons: [
      "bold",
      "italic",
      "underline",
      "strikethrough",
      "align",
      "outdent",
      "indent",
      "font",
      "fontsize",
      "paragraph",
      "image",
      "link",
      "undo",
      "redo",
    ],
    height: 400,
  };

  return (
    <Card className="shadow-lg mt-3 p-4 text-start">
      <Form onSubmit={handleSubmit} encType="multipart/form-data">
        <Row className="mb-3">
          <Col md={12}>
            <Form.Group>
              <RichTextEditor
                label="Title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                placeholder="Enter blog title"
                rows={2}
                required
              />
            </Form.Group>
          </Col>
          {/* <Col md={4}>
            <Form.Group>
              <Form.Label>Internal Link</Form.Label>
              <Form.Control
                type="url"
                name="internal_link"
                value={formData.internal_link}
                onChange={handleChange}
              />
            </Form.Group>
          </Col> */}
          {/* <Col md={4}>
            <Form.Group>
              <Form.Label>Similar Article</Form.Label>
              <Form.Control
                type="url"
                name="external_link"
                value={formData.external_link}
                onChange={handleChange}
              />
            </Form.Group>
          </Col> */}
        </Row>
        <Row className="mb-3">
          <Col md={6}>
            <Form.Group>
              <Form.Label>Meta Title</Form.Label>
              <Form.Control
                type="text"
                name="meta_title"
                value={formData.meta_title}
                onChange={handleChange}
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group>
              <Form.Label>Meta Keywords</Form.Label>
              <Form.Control
                type="text"
                name="meta_keywords"
                value={formData.meta_keywords}
                onChange={handleChange}
                placeholder="Comma-separated keywords"
              />
            </Form.Group>
          </Col>
          {/* <Col md={4}>
            <Form.Group>
              <Form.Label>Canonical URL</Form.Label>
              <Form.Control
                type="url"
                name="canonical_url"
                value={formData.canonical_url}
                onChange={handleChange}
              />
            </Form.Group>
          </Col> */}
        </Row>
        <Row className="mb-3">
          <Col md={6}>
            <Form.Group>
              <RichTextEditor
                label="Meta Description"
                name="meta_description"
                value={formData.meta_description}
                onChange={handleChange}
                placeholder="Enter meta description"
                rows={3}
                required
              />
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group>
              <RichTextEditor
                label="Short Content"
                name="short_content"
                value={formData.short_content}
                onChange={handleChange}
                placeholder="60 words"
                rows={3}
                required
              />
            </Form.Group>
          </Col>
        </Row>
        <Form.Group className="mb-3">
          <Form.Label>Content</Form.Label>
          <JoditEditor
            ref={editorRef}
            value={formData.content}
            config={editorConfig}
            tabIndex={1}
            onBlur={(newContent) => handleJoditChange(newContent)}
            required
          />
        </Form.Group>
        <Row className="mb-3 mt-5">
          <Col md={6}>
            <Form.Group>
              <Form.Label>Image</Form.Label>
              <Form.Control
                type="file"
                name="image"
                onChange={handleFileChange}
              />
              {imageError && <div className="text-danger">{imageError}</div>}
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group>
              <Form.Label>Image Caption</Form.Label>
              <Form.Control
                type="text"
                name="image_caption"
                value={formData.image_caption}
                onChange={handleChange}
              />
            </Form.Group>
          </Col>
        </Row>
        {preview && (
          <div className="mb-3 text-center position-relative">
            <img
              src={preview}
              alt="Preview"
              style={{ maxWidth: "100%", maxHeight: "300px" }}
            />
            <button
              type="button"
              className="btn btn-danger position-absolute"
              style={{ top: 10, right: 10 }}
              onClick={handleRemoveImage}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="3.5"
                stroke="currentColor"
                className="text-white"
                style={{ width: "20px", height: "20px" }}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M6 18 18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        )}
        <Form.Group className="mb-3">
          <RichTextEditor
            label="Introduction"
            name="introduction"
            value={formData.introduction}
            onChange={handleChange}
            placeholder="Enter blog introduction"
            rows={4}
            required
          />
        </Form.Group>
        <Button
          variant="outline-primary"
          type="submit"
          className="w-100"
          disabled={loading}
        >
          {loading
            ? "Submitting..."
            : initialValues.id
            ? "Update Blog"
            : "Create Blog"}
        </Button>
      </Form>
    </Card>
  );
};

export default EditBlogForm;
