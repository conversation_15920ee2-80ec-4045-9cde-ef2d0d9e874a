import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON>, <PERSON><PERSON>, Alert, Spinner, Card } from 'react-bootstrap';
import { createCourse } from '../../redux/slice/courseSlice';

const CourseForm = () => {
  const dispatch = useDispatch();
  const { isLoading, error, course } = useSelector((state) => state.course);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    dispatch(createCourse(formData));
  };

  return (
    <div className="container d-flex justify-content-center" style={{ margin: "7rem 0rem 3rem 0rem" }}>
      <Card style={{ width: '100%', maxWidth: '500px' }} className="p-4 shadow-sm">
        <h2 className="text-center mb-4">Create Course</h2>
        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Course Name</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter course name"
              name="name"
              value={formData.name}
              onChange={handleChange}
            />
          </Form.Group>
          <Form.Group className="mb-3">
            <Form.Label>Description</Form.Label>
            <Form.Control
              as="textarea"
              rows={4}
              placeholder="Enter course description"
              name="description"
              value={formData.description}
              onChange={handleChange}
            />
          </Form.Group>
          <Button variant="primary" type="submit" disabled={isLoading} className="w-100 mt-3">
            {isLoading ? <Spinner animation="border" size="sm" /> : 'Submit'}
          </Button>
        </Form>
        {error && <Alert variant="danger" className="mt-3">{error}</Alert>}
        {course && <Alert variant="success" className="mt-3">Course created successfully!</Alert>}
      </Card>
    </div>
  );
};

export default CourseForm;
