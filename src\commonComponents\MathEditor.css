/* Math Editor Styles */
.math-input-container {
  position: relative;
}

.math-input-container math-field {
  font-family: 'Latin Modern Math', 'STIX Two Math', 'TeX Gyre Termes Math', 'Computer Modern', serif;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.math-input-container math-field:focus {
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.math-preview {
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
}

.math-preview-placeholder {
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
}

.math-preview-error {
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 0.375rem;
}

.math-editor .card-header h6 {
  color: #495057;
  font-weight: 600;
}

/* Virtual Keyboard Styles */
.ML__keyboard {
  z-index: 1060 !important; /* Higher than Bootstrap modal z-index */
}

/* Ensure math content is properly sized in modals */
.modal .math-editor {
  font-size: 0.9rem;
}

.modal .math-input-container math-field {
  min-height: 40px;
  font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .math-editor .card-body .row {
    flex-direction: column;
  }
  
  .math-editor .card-body .col-md-6 {
    margin-bottom: 1rem;
  }
}

/* KaTeX styling adjustments */
.katex {
  font-size: 1.1em;
}

.katex-display {
  margin: 0.5em 0;
}

/* Math content in alerts */
.alert .katex {
  font-size: 0.9em;
}

/* Ensure proper spacing in form groups */
.math-editor .form-group {
  margin-bottom: 1rem;
}

.math-editor .form-label {
  margin-bottom: 0.5rem;
  font-weight: 500;
}

/* Button styling in math editor */
.math-editor .btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Code styling for LaTeX display */
.math-editor code {
  background-color: #f8f9fa;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875em;
}

/* Math field placeholder styling */
math-field::part(placeholder) {
  color: #6c757d;
  opacity: 1;
}
