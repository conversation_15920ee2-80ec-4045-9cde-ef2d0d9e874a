import React, { useEffect, useState } from 'react'; 
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, Link } from 'react-router-dom';
import { toast, Toaster } from 'react-hot-toast';
import { <PERSON><PERSON>, Row, Col, Container } from 'react-bootstrap';
import NavigationBar from "../../../commonComponents/NavigationBar";
import CreateCurrentAffairsQuestion from '../components/CreateCurrentAffairsQuestion';
import ViewCurrentAffailsQuestions from '../components/ViewCurrentAffailsQuestions';
import QuestionsNav from '../../../commonComponents/QuestionsNav'; // Import the QuestionsNav component
import CurrentAffairsQuestionsNav from '../../../commonComponents/CurrentAffairsQuestionsNav';

export default function CurrentAffairsQuestionsDashboard() {

  const navigate = useNavigate();
  const dispatch = useDispatch(); 
  const accessToken = useSelector((state) => state.contributor.accessToken);

  // Check for access token and redirect if necessary
  useEffect(() => {
    if (!accessToken) {
      toast.error('Please log in as a contributor!');
      const timer = setTimeout(() => {
        navigate('/contributor_login'); 
      }, 2000);
      return () => clearTimeout(timer); 
    }
  }, [accessToken, navigate]);

  // Conditional rendering to prevent rendering when no access token
  if (!accessToken) {
    return (
      <>
        <section>
          <Toaster />
        </section>
      </>
    ); 
  }

  const [searchTerm, setSearchTerm] = useState(''); // State for the search term
  const [radioValue, setRadioValue] = useState('1'); // State for radio buttons
  const [optionAddedFlag, setOptionAddedFlag] = useState(false); // Track if an option was added

  // Handle the change in the Question Content input field and pass it as search term
  const handleQuestionContentChange = (content) => {
    setSearchTerm(content); // Update search term when the content changes
  };

  // Handle change in search bar
  const handleSearchTermChange = (term) => {
    setSearchTerm(term); // Update search term from the search bar
  };

  // Function to set the flag when an option is added
  const handleOptionAdded = () => {
    setOptionAddedFlag((prev) => !prev); // Toggle flag to trigger re-fetch
  };

  return (
    <>
      <NavigationBar />
      <Container className="mt-4">
        <Row>
          <Col xs={12} sm={10} md={5} lg={5}>
            {/* Replace the ButtonGroup with QuestionsNav */}
            {/* <CurrentAffairsQuestionsNav radioValue={radioValue} setRadioValue={setRadioValue} /> */}

            <CreateCurrentAffairsQuestion
              onQuestionContentChange={handleQuestionContentChange}
              onOptionAdd={handleOptionAdded}
            />
            <div className="d-flex justify-content-center">
              <Link to="/contributor_dashboard">
                <Button variant="secondary" className="my-5 text-center">
                  Back to Dashboard
                </Button>
              </Link>
            </div>
          </Col>
          <Col>
            <ViewCurrentAffailsQuestions
              searchTerm={searchTerm} // Pass searchTerm to ViewCurrentAffailsQuestions
              onSearchTermChange={handleSearchTermChange} // Pass handleSearchTermChange to ViewCurrentAffailsQuestions
              optionAddedFlag={optionAddedFlag} // Pass the flag to trigger re-fetch
            />
          </Col>
        </Row>
      </Container>
    </>
  );
}
