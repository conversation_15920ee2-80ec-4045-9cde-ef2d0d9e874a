# Authentication System Documentation

## Overview

This application now includes an automatic token expiration handling system that will:

1. **Automatically detect expired tokens** when making API calls
2. **Clear user session** when token expires
3. **Redirect to login page** with appropriate error message
4. **Prevent multiple simultaneous redirects**

## How It Works

### 1. Axios Response Interceptor

The system uses an Axios response interceptor (`src/utils/axiosInterceptor.js`) that:

- Monitors all HTTP responses
- Detects 401 (Unauthorized) and 403 (Forbidden) status codes
- Automatically clears Redux state and redirects to login

### 2. Authentication Hook

The `useAuth` hook (`src/hooks/useAuth.js`) provides:

- Automatic authentication checking
- Route protection
- User session management
- Role-based access control

## Usage Examples

### Basic Component Protection

```javascript
import { useContributorAuth } from "../../hooks/useAuth";

const MyComponent = () => {
  const auth = useContributorAuth();
  
  // Don't render if not authenticated (hook handles redirect)
  if (!auth.isAuthenticated) {
    return null;
  }

  return (
    <div>
      <h1>Welcome, {auth.profile?.name}!</h1>
      {/* Your component content */}
    </div>
  );
};
```

### Using Higher-Order Component (HOC)

```javascript
import { withAuth } from "../../hooks/useAuth";

const MyComponent = ({ auth }) => {
  return (
    <div>
      <h1>Welcome, {auth.profile?.name}!</h1>
      <button onClick={auth.logout}>Logout</button>
    </div>
  );
};

// Wrap component with authentication
export default withAuth(MyComponent);
```

### Role-Based Access Control

```javascript
const AdminComponent = () => {
  const auth = useContributorAuth();
  
  if (!auth.isAuthenticated) return null;
  
  if (!auth.hasRole('admin')) {
    return <div>Access denied. Admin role required.</div>;
  }

  return <div>Admin content here</div>;
};
```

### Custom Authentication Options

```javascript
import { useAuth } from "../../hooks/useAuth";

const OptionalAuthComponent = () => {
  const auth = useAuth({
    requireAuth: false,        // Don't require authentication
    redirectTo: '/custom-login', // Custom redirect path
    showToast: false          // Don't show toast messages
  });

  return (
    <div>
      {auth.isAuthenticated ? (
        <p>Welcome back, {auth.profile?.name}!</p>
      ) : (
        <p>Please log in to access more features.</p>
      )}
    </div>
  );
};
```

## API Reference

### useAuth Hook

```javascript
const auth = useAuth(options);
```

**Options:**
- `requireAuth` (boolean): Whether authentication is required (default: true)
- `redirectTo` (string): Redirect path for unauthenticated users (default: '/contributor_login')
- `showToast` (boolean): Show toast messages (default: true)

**Returns:**
- `isAuthenticated` (boolean): Whether user is authenticated
- `accessToken` (string): Current access token
- `profile` (object): User profile data
- `role` (string): User role
- `loading` (boolean): Authentication loading state
- `logout` (function): Manual logout function
- `hasRole` (function): Check if user has specific role
- `hasAnyRole` (function): Check if user has any of specified roles

### useContributorAuth Hook

Convenience hook with default settings for contributor authentication:

```javascript
const auth = useContributorAuth();
```

### withAuth HOC

```javascript
const ProtectedComponent = withAuth(MyComponent, options);
```

Same options as `useAuth` hook.

## Automatic Features

### Token Expiration Detection

The system automatically detects expired tokens through:

1. **HTTP 401 responses**: Unauthorized access
2. **HTTP 403 responses**: Forbidden access (when related to token issues)
3. **JWT expiration checking**: For JWT tokens (optional)

### Session Cleanup

When token expiration is detected:

1. Redux state is cleared using `handleTokenExpiration` action
2. User is redirected to login page
3. Appropriate error message is shown
4. Multiple redirects are prevented

### Persistence Management

The system includes automatic persistence cleanup:

- Persisted state expires after 6 hours
- Automatic cleanup on app startup
- Hourly checks for expired persistence

## Migration Guide

### From Old Pattern

**Old way:**
```javascript
const MyComponent = () => {
  const navigate = useNavigate();
  const accessToken = useSelector((state) => state.contributor.accessToken);

  useEffect(() => {
    if (!accessToken) {
      toast.error('Please log in as a contributor!');
      const timer = setTimeout(() => {
        navigate('/contributor_login');
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [accessToken, navigate]);

  if (!accessToken) {
    return <div>Loading...</div>;
  }

  return <div>Component content</div>;
};
```

**New way:**
```javascript
const MyComponent = () => {
  const auth = useContributorAuth();
  
  if (!auth.isAuthenticated) return null;
  
  return <div>Component content</div>;
};
```

## Benefits

1. **Automatic handling**: No need to manually check tokens in every component
2. **Consistent behavior**: All components handle authentication the same way
3. **Better UX**: Immediate redirect on token expiration
4. **Reduced boilerplate**: Less repetitive authentication code
5. **Centralized logic**: All authentication logic in one place
6. **Role-based access**: Easy role checking utilities

## Troubleshooting

### Common Issues

1. **Multiple redirects**: The system prevents this automatically
2. **Token not clearing**: Check if interceptor is properly initialized
3. **Redirect not working**: Ensure `setupAxiosInterceptors()` is called in main.jsx

### Debug Mode

To debug authentication issues, check:

1. Browser console for interceptor logs
2. Redux DevTools for state changes
3. Network tab for 401/403 responses
