import React from 'react';
import { ButtonGroup, ToggleButton, Row, Col } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';

const QuestionsNav = ({ radioValue, setRadioValue }) => {
  const navigate = useNavigate(); // Hook for navigation

  const radios = [
    { name: 'Normal Ques.', value: '1', path: '/questions_dashboard' },
    { name: 'Master Ques.', value: '2', path: '/master_questions_dashboard' },
    { name: 'Master Option', value: '3', path: '/master_options_dashboard' },
    { name: 'Current Affairs Ques.', value: '4', path: '/all_blogs' },
    { name: 'Previous Year Questions', value: '5', path: '/previous_year_questions_dashboard' },
  ];

  const handleRadioChange = (value, path) => {
    setRadioValue(value);
    navigate(path); // Navigate to the selected page
  };

  return (
    <div>
      {/* First Row of Buttons */}
      <Row className="mb-1">
        <Col>
          <ButtonGroup>
            {radios.slice(0, 3).map((radio, idx) => (
              <ToggleButton
                key={idx}
                id={`radio-${idx}`}
                type="radio"
                variant={idx % 2 ? 'outline-success' : 'outline-primary'}
                name="radio"
                value={radio.value}
                checked={radioValue === radio.value}
                onChange={() => handleRadioChange(radio.value, radio.path)}
              >
                {radio.name}
              </ToggleButton>
            ))}
          </ButtonGroup>
        </Col>
      </Row>

      {/* Second Row of Buttons */}
      <Row>
        <Col>
          <ButtonGroup>
            {radios.slice(3).map((radio, idx) => (
              <ToggleButton
                key={idx + 3} // To ensure unique keys in second group
                id={`radio-${idx + 3}`}
                type="radio"
                variant={idx % 2 ? 'outline-success' : 'outline-primary'}
                name="radio"
                value={radio.value}
                checked={radioValue === radio.value}
                onChange={() => handleRadioChange(radio.value, radio.path)}
              >
                {radio.name}
              </ToggleButton>
            ))}
          </ButtonGroup>
        </Col>
      </Row>
    </div>
  );
};

export default QuestionsNav;
