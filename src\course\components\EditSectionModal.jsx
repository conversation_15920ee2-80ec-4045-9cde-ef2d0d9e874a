import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form, Spinner } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { getTestPatterns } from "../../redux/slice/paperEngineSlice"; // Action to fetch test patterns

const EditSectionModal = ({ show, onHide, section, onEditChange, onSubmit }) => {
  const dispatch = useDispatch();
  const { testPatterns, status: testPatternsStatus } = useSelector(
    (state) => state.paperEngine
  ); // Access test patterns from Redux store

  const [testPatternValue, setTestPatternValue] = useState(""); // Local state for selected test pattern

  // Fetch test patterns when the modal opens
  useEffect(() => {
    if (testPatternsStatus === "idle") {
      dispatch(getTestPatterns()); // Fetch test patterns if not already fetched
    }
  }, [dispatch, testPatternsStatus, show]);

  // Prefill the test pattern based on the section's test_pattern ID
  useEffect(() => {
    if (section && section.test_pattern !== undefined) {
      setTestPatternValue(section.test_pattern); // Prefill with the test pattern ID from section data
    }
  }, [section]);

  const handleTestPatternChange = (e) => {
    setTestPatternValue(e.target.value); // Update selected test pattern
  };

  const handleSubmit = (e) => {
    e.preventDefault(); // Prevent default form submission

    const updatedSectionData = {
      paper: section.paper,
      name: section.name,
      description: section.description,
      max_marks: section.max_marks,
      number_of_questions: section.number_of_questions,
      test_pattern: testPatternValue || null, // Set to null if "None" is selected
    };

    onSubmit(updatedSectionData); // Call the onSubmit function passed from the parent
  };

  // Find the selected test pattern name from the list
  const selectedTestPattern = testPatterns.find(
    (pattern) => pattern.pattern_id === testPatternValue
  );

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Section</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {(!section || !section.name) ? (
          <h5>No section selected for editing.</h5>
        ) : (
          <Form onSubmit={handleSubmit}>
            {/* Section Name */}
            <Form.Group className="mb-3">
              <Form.Label>Section Name</Form.Label>
              <Form.Control
                type="text"
                name="name"
                value={section.name}
                onChange={onEditChange}
                placeholder="Enter section name"
                required
              />
            </Form.Group>

            {/* Description */}
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                type="text"
                name="description"
                value={section.description}
                onChange={onEditChange}
                placeholder="Enter section description"
                required
              />
            </Form.Group>

            {/* Max Marks */}
            <Form.Group className="mb-3">
              <Form.Label>Max Marks</Form.Label>
              <Form.Control
                type="number"
                name="max_marks"
                value={section.max_marks}
                onChange={onEditChange}
                placeholder="Enter max marks"
                required
              />
            </Form.Group>

            {/* Number of Questions */}
            <Form.Group className="mb-3">
              <Form.Label>Number of Questions</Form.Label>
              <Form.Control
                type="number"
                name="number_of_questions"
                value={section.number_of_questions}
                onChange={onEditChange}
                placeholder="Enter number of questions"
                required
              />
            </Form.Group>

            {/* Test Pattern */}
            <h6 className="mb-3">
              {selectedTestPattern
                ? `Current Test Pattern: ${selectedTestPattern.name} (Version: ${selectedTestPattern.version})`
                : "No test pattern is selected"}
            </h6>

            <Form.Group className="mb-3">
              <Form.Label>Test Pattern</Form.Label>
              {testPatternsStatus === "loading" ? (
                <Spinner animation="border" variant="primary" />
              ) : (
                <Form.Control
                  as="select"
                  value={testPatternValue}
                  onChange={handleTestPatternChange}
                >
                  <option value="">None</option> {/* Option for "None" */}
                  {testPatterns.map((pattern) => (
                    <option key={pattern.pattern_id} value={pattern.pattern_id}>
                      {pattern.name} (Version: {pattern.version})
                    </option>
                  ))}
                </Form.Control>
              )}
            </Form.Group>

            <Button variant="outline-primary w-100" type="submit">
              Save Changes
            </Button>
          </Form>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default EditSectionModal;
